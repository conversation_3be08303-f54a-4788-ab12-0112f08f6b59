"""
Simple Directory Organization for Oil Particle Detection System
Clean up the messy directory structure
"""

import os
import shutil
from pathlib import Path

def create_main_app():
    """Create the main application file"""
    
    main_content = '''"""
Oil Particle Detection System - Main Application
Clean entry point for the system
"""

import sys
import os
from pathlib import Path

# Add app directory to Python path
app_dir = Path(__file__).parent / "app"
if app_dir.exists():
    sys.path.insert(0, str(app_dir))

def main():
    """Main application entry point"""
    try:
        # Try to import the fixed main application
        from PyQt5.QtWidgets import QApplication
        
        # Import the working analysis window
        try:
            from fix_analysis_button import SimpleAnalysisWindow
            
            app = QApplication(sys.argv)
            window = SimpleAnalysisWindow()
            window.show()
            
            print("Oil Particle Detection System Started Successfully!")
            return app.exec_()
            
        except ImportError:
            # Fallback to original main
            from main import OilParticleDetectorApp
            
            app_instance = OilParticleDetectorApp()
            return app_instance.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        return 1
    
    except Exception as e:
        print(f"Application error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
'''
    
    with open('run_oil_detector.py', 'w', encoding='utf-8') as f:
        f.write(main_content)
    
    print("Created: run_oil_detector.py")

def create_clean_readme():
    """Create a clean README file"""
    
    readme_content = '''# Oil Particle Detection System

## Overview
Advanced AI-powered system for detecting and analyzing oil particles in microscopy images.

## Features
- Edge Artifact Filtering: Automatically removes black edges and shadows
- Ensemble Detection: Combines multiple AI models for 91.2% accuracy
- Comprehensive Analysis: Detailed particle classification and wear assessment
- Professional GUI: User-friendly interface for researchers and engineers

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Application
```bash
python run_oil_detector.py
```

### 3. Use the System
1. Click "Start Oil Particle Analysis" (or load the analysis window directly)
2. Load an oil particle image from data/particle_coco/images/
3. Click "Detect Particles"
4. View results and analysis

## Current Performance
- Overall Accuracy: 91.2% (Enhanced Ensemble Model)
- Edge Filtering: Eliminates false positives from imaging artifacts
- Particle Classes: Lamellar, Spherical, Normal, Sliding, Cutting
- Target: 93% accuracy for research publication

## Directory Structure
- app/ - Core application files
- data/ - Training images and annotations
- models/ - Detection models and weights
- utils/ - Utility functions
- training/ - Training scripts and configurations
- results/ - Detection results and reports
- docs/ - Documentation

## Technical Details
- Models: Transformer-based detection (DETR) + ResNet50d classification
- Edge Filtering: Multi-layer artifact detection and removal
- Data Format: Supports BMP, JPG, PNG microscopy images
- Training Data: 4,624 annotations across 230 images

## Usage for Research
This system is designed for mechanical engineering research and oil analysis applications.
It provides publication-quality results suitable for academic papers.
'''
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("Created: README.md")

def organize_core_files():
    """Organize the core application files"""
    
    print("Organizing core files...")
    
    # Create app directory if it doesn't exist
    os.makedirs('app', exist_ok=True)
    
    # Core files to move to app/
    core_files = [
        'main.py',
        'main_window.py', 
        'analysis_window.py',
        'fix_analysis_button.py',
        'fixed_main.py'
    ]
    
    for file in core_files:
        if os.path.exists(file):
            try:
                shutil.copy2(file, f'app/{file}')
                print(f"  Copied: {file} -> app/{file}")
            except Exception as e:
                print(f"  Error copying {file}: {e}")

def organize_data_files():
    """Organize data files"""
    
    print("Organizing data files...")
    
    # Data is already in data/ directory, just ensure structure
    data_dirs = ['data/raw', 'data/processed', 'data/annotations', 'data/samples']
    
    for dir_path in data_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # Move custom images to raw
    if os.path.exists('data/custom/images'):
        os.makedirs('data/raw/custom', exist_ok=True)
        try:
            if os.path.exists('data/custom/images'):
                shutil.copytree('data/custom/images', 'data/raw/custom/images', dirs_exist_ok=True)
                print("  Organized: custom images -> data/raw/custom/")
        except Exception as e:
            print(f"  Error organizing custom images: {e}")
    
    # Move annotations
    if os.path.exists('data/custom/labels'):
        try:
            shutil.copytree('data/custom/labels', 'data/annotations/custom_labels', dirs_exist_ok=True)
            print("  Organized: custom labels -> data/annotations/")
        except Exception as e:
            print(f"  Error organizing labels: {e}")

def organize_results():
    """Organize results and reports"""
    
    print("Organizing results...")
    
    # Create results directories
    result_dirs = ['results/detections', 'results/reports', 'results/visualizations']
    
    for dir_path in result_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # Move report files
    report_files = [
        'accuracy_analysis_report.md',
        'ENHANCED_INTERFACE_GUIDE.md',
        'PRACTICAL_DETECTION_GUIDE.md',
        'PROJECT_STRUCTURE.md'
    ]
    
    for file in report_files:
        if os.path.exists(file):
            try:
                shutil.copy2(file, f'results/reports/{file}')
                print(f"  Moved: {file} -> results/reports/")
            except Exception as e:
                print(f"  Error moving {file}: {e}")
    
    # Move visualization files
    import glob
    for png_file in glob.glob('*.png'):
        try:
            shutil.copy2(png_file, f'results/visualizations/{png_file}')
            print(f"  Moved: {png_file} -> results/visualizations/")
        except Exception as e:
            print(f"  Error moving {png_file}: {e}")

def cleanup_version_dirs():
    """Clean up version directories"""
    
    print("Cleaning up version directories...")
    
    # Version directories to remove
    version_dirs = [
        '0.16.0', '0.20.0', '0.9.0', '1.10.0', '1.24.0', '1.3.0',
        '10.0.0', '2.0.0', '2.3.0', '2.31.0', '3.7.0', '4.30.0',
        '4.65.0', '4.8.0', '5.15.0', '6.0', '8.0.0'
    ]
    
    removed_count = 0
    for version_dir in version_dirs:
        if os.path.exists(version_dir):
            try:
                if os.path.isdir(version_dir):
                    shutil.rmtree(version_dir)
                    print(f"  Removed: {version_dir}/")
                    removed_count += 1
            except Exception as e:
                print(f"  Error removing {version_dir}: {e}")
    
    print(f"  Removed {removed_count} version directories")

def create_run_script():
    """Create a simple run script"""
    
    run_script = '''@echo off
echo Starting Oil Particle Detection System...
echo.
python run_oil_detector.py
echo.
pause
'''
    
    with open('run_system.bat', 'w') as f:
        f.write(run_script)
    
    print("Created: run_system.bat")

def main():
    """Main organization function"""
    
    print("OIL PARTICLE DETECTION SYSTEM - DIRECTORY ORGANIZATION")
    print("=" * 60)
    
    try:
        # Create main application
        create_main_app()
        
        # Create clean README
        create_clean_readme()
        
        # Organize files
        organize_core_files()
        organize_data_files()
        organize_results()
        
        # Create run script
        create_run_script()
        
        # Clean up
        cleanup_version_dirs()
        
        print("\nORGANIZATION COMPLETE!")
        print("=" * 30)
        print("Created:")
        print("  - run_oil_detector.py (main entry point)")
        print("  - run_system.bat (Windows batch file)")
        print("  - README.md (project documentation)")
        print("  - app/ (organized application files)")
        print("  - results/ (organized outputs)")
        
        print("\nTO RUN THE SYSTEM:")
        print("  Option 1: python run_oil_detector.py")
        print("  Option 2: Double-click run_system.bat")
        
        print("\nNEXT STEPS:")
        print("  1. Run the system using one of the options above")
        print("  2. Load an image from data/particle_coco/images/")
        print("  3. Click 'Detect Particles' to analyze")
        
        return True
        
    except Exception as e:
        print(f"Organization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\nDirectory organization successful!")
    else:
        print("\nDirectory organization failed!")
