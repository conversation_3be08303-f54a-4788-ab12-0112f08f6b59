"""
Oil Particle Detection System - Application Builder
Creates a standalone executable application using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required build dependencies are installed"""
    
    print("🔍 Checking build dependencies...")
    
    required_packages = [
        'PyInstaller',
        'PyQt5',
        'opencv-python',
        'numpy',
        'pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n📦 Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies available")
    return True

def create_spec_file():
    """Create PyInstaller spec file for the application"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Application details
app_name = "OilParticleDetector"
main_script = "oil_particle_detector_app.py"

# Data files to include
data_files = [
    ('app', 'app'),
    ('src', 'src'),
    ('data', 'data'),
    ('docs', 'docs'),
    ('requirements', 'requirements'),
    ('README.md', '.'),
    ('APPLICATION_INFO.md', '.'),
    ('DIRECTORY_GUIDE.md', '.'),
    ('STANDALONE_APPLICATION_GUIDE.md', '.')
]

# Hidden imports (modules that PyInstaller might miss)
hidden_imports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'cv2',
    'numpy',
    'PIL',
    'json',
    'pathlib',
    'datetime',
    'logging',
    'traceback',
    'models.practical_detector',
    'models.rt_detr_detector',
    'models.particle_analyzer',
    'models.transformer_detector',
    'transformers',
    'torch',
    'torchvision',
    'sklearn',
    'scipy'
]

block_cipher = None

a = Analysis(
    [main_script],
    pathex=[],
    binaries=[],
    datas=data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name=app_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Set to False for GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if Path('app_icon.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=app_name,
)
'''
    
    with open('oil_particle_detector.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ Created PyInstaller spec file: oil_particle_detector.spec")

def create_app_icon():
    """Create application icon"""
    
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple icon
        size = (256, 256)
        image = Image.new('RGBA', size, (70, 130, 180, 255))  # Steel blue
        draw = ImageDraw.Draw(image)
        
        # Draw a simple microscope-like icon
        # Outer circle
        draw.ellipse([50, 50, 206, 206], fill=(255, 255, 255, 255), outline=(40, 40, 40, 255), width=4)
        
        # Inner circle (lens)
        draw.ellipse([80, 80, 176, 176], fill=(200, 220, 255, 255), outline=(40, 40, 40, 255), width=3)
        
        # Particle dots
        particles = [(110, 110), (130, 140), (150, 120), (120, 150)]
        for x, y in particles:
            draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 100, 100, 255))
        
        # Save as ICO
        image.save('app_icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Created application icon: app_icon.ico")
        
    except ImportError:
        print("⚠️ PIL not available, skipping icon creation")
    except Exception as e:
        print(f"⚠️ Icon creation failed: {e}")

def build_application():
    """Build the standalone application"""
    
    print("\n🔨 Building standalone application...")
    
    # Clean previous builds
    build_dirs = ['build', 'dist', '__pycache__']
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  🧹 Cleaned {dir_name}/")
    
    # Run PyInstaller
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'oil_particle_detector.spec'
        ]
        
        print(f"  🚀 Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Application built successfully!")
            return True
        else:
            print(f"❌ Build failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_installer_script():
    """Create installer script for the application"""
    
    installer_content = '''@echo off
title Oil Particle Detection System v2.0 - Installer
color 0A

echo.
echo ================================================================
echo    OIL PARTICLE DETECTION SYSTEM v2.0 - INSTALLER
echo ================================================================
echo.
echo 🔬 Professional Oil Particle Detection Application
echo 📊 Advanced AI-powered analysis with 91.2%% accuracy
echo.

echo 📋 Installation Options:
echo.
echo 1. Install to Program Files (Recommended)
echo 2. Install to Custom Location
echo 3. Portable Installation (Current Directory)
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    set "install_dir=%ProgramFiles%\\Oil Particle Detection System"
    echo Installing to: %install_dir%
    mkdir "%install_dir%" 2>nul
    xcopy /E /I /Y "OilParticleDetector" "%install_dir%"
    
    REM Create Start Menu shortcut
    echo Creating Start Menu shortcut...
    powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs\\Oil Particle Detection System.lnk'); $Shortcut.TargetPath = '%install_dir%\\OilParticleDetector.exe'; $Shortcut.WorkingDirectory = '%install_dir%'; $Shortcut.Description = 'Oil Particle Detection System v2.0'; $Shortcut.Save()}"
    
    REM Create Desktop shortcut
    echo Creating Desktop shortcut...
    powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Oil Particle Detection System.lnk'); $Shortcut.TargetPath = '%install_dir%\\OilParticleDetector.exe'; $Shortcut.WorkingDirectory = '%install_dir%'; $Shortcut.Description = 'Oil Particle Detection System v2.0'; $Shortcut.Save()}"
    
    echo.
    echo ✅ Installation completed successfully!
    echo 🚀 You can now launch the application from:
    echo    • Start Menu
    echo    • Desktop shortcut
    echo    • %install_dir%\\OilParticleDetector.exe
    
) else if "%choice%"=="2" (
    set /p "install_dir=Enter installation directory: "
    echo Installing to: %install_dir%
    mkdir "%install_dir%" 2>nul
    xcopy /E /I /Y "OilParticleDetector" "%install_dir%"
    echo ✅ Installation completed to: %install_dir%
    
) else if "%choice%"=="3" (
    echo ✅ Portable installation ready!
    echo 🚀 Run OilParticleDetector.exe to start the application
    
) else (
    echo ❌ Invalid choice. Please run the installer again.
)

echo.
echo 📖 Application Features:
echo    • Professional GUI interface
echo    • Advanced particle detection (91.2%% accuracy)
echo    • Multiple detection models
echo    • Comprehensive analysis reports
echo    • Export capabilities (JSON, CSV, PDF)
echo    • Detection history tracking
echo.
pause
'''
    
    with open('dist/install.bat', 'w') as f:
        f.write(installer_content)
    
    print("✅ Created installer script: dist/install.bat")

def create_readme():
    """Create README for the built application"""
    
    readme_content = '''# Oil Particle Detection System v2.0 - Standalone Application

## 🚀 Quick Start

### Windows Installation
1. Run `install.bat` for guided installation
2. Or directly run `OilParticleDetector.exe` for portable use

### First Launch
1. Double-click `OilParticleDetector.exe`
2. Wait for the application to load
3. Go to Detection tab and browse for an oil sample image
4. Configure detection settings and click "Start Detection"

## 📁 Application Structure

```
OilParticleDetector/
├── OilParticleDetector.exe     # Main application
├── app/                        # Application components
├── data/                       # Sample data and models
├── docs/                       # Documentation
├── requirements/               # Dependencies info
├── README.md                   # This file
└── _internal/                  # Application libraries
```

## 🎯 Features

- **Professional GUI**: Modern tabbed interface
- **Advanced Detection**: 91.2% accuracy with Enhanced Ensemble
- **Multiple Models**: RT-DETR, Transformer, Practical CV
- **Edge Filtering**: Removes imaging artifacts
- **Export Options**: JSON, CSV, Excel, PDF reports
- **History Tracking**: Complete detection history
- **Batch Processing**: Handle multiple images

## 🔧 System Requirements

- **OS**: Windows 10/11 (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Display**: 1280x720 minimum resolution

## 📞 Support

For technical support or research collaboration:
- Check APPLICATION_INFO.md for detailed documentation
- Review DIRECTORY_GUIDE.md for system organization
- Contact: Mechanical Engineering Research Team

## 🎓 Academic Use

This application is designed for mechanical engineering research and is suitable for:
- Oil particle analysis studies
- Wear analysis publications
- Academic paper development
- Research collaboration

Current accuracy: 91.2% (Enhanced Ensemble Model)
Target accuracy: 93% for publication

---
Oil Particle Detection System v2.0 - Professional Edition
Advanced AI-Powered Particle Detection for Mechanical Engineering Research
'''
    
    with open('dist/OilParticleDetector/README.md', 'w') as f:
        f.write(readme_content)
    
    print("✅ Created application README")

def main():
    """Main build process"""
    
    print("🏗️ OIL PARTICLE DETECTION SYSTEM - APPLICATION BUILDER")
    print("=" * 70)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Build failed: Missing dependencies")
        return False
    
    # Create build files
    print("\n📝 Creating build configuration...")
    create_app_icon()
    create_spec_file()
    
    # Build application
    if not build_application():
        print("\n❌ Build failed")
        return False
    
    # Create additional files
    print("\n📦 Creating installation package...")
    create_installer_script()
    
    if os.path.exists('dist/OilParticleDetector'):
        create_readme()
        
        # Copy additional files
        additional_files = [
            'APPLICATION_INFO.md',
            'DIRECTORY_GUIDE.md'
        ]
        
        for file in additional_files:
            if os.path.exists(file):
                shutil.copy2(file, 'dist/OilParticleDetector/')
                print(f"  📄 Copied {file}")
    
    print("\n🎉 BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print("📁 Application location: dist/OilParticleDetector/")
    print("🚀 Run: dist/OilParticleDetector/OilParticleDetector.exe")
    print("💿 Installer: dist/install.bat")
    print("\n✅ Your standalone application is ready!")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ Build process failed!")
        sys.exit(1)
    
    input("\nPress Enter to close...")
