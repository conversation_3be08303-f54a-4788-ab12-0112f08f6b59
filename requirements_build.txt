# Build Requirements for Oil Particle Detection System
# Install these packages to build the standalone application

# Core application dependencies
PyQt5>=5.15.0
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0

# Application building
PyInstaller>=5.0.0

# Optional for enhanced features
matplotlib>=3.5.0
pandas>=1.3.0
scikit-image>=0.18.0

# Development and testing
pytest>=6.0.0
pytest-qt>=4.0.0

# Documentation
markdown>=3.3.0

# Windows-specific (for shortcuts)
pywin32>=227; sys_platform == "win32"
winshell>=0.6; sys_platform == "win32"
