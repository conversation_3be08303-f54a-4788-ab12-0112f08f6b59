#!/usr/bin/env python3
"""
Comprehensive Model Accuracy and Loss Evaluation for Oil Particle Detection System
"""

import sys
import os
import json
import time
import numpy as np
import cv2
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix
import pandas as pd

class ModelEvaluator:
    """Comprehensive model evaluation class"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.results = {}
        
    def evaluate_resnet50d_accuracy(self) -> Dict:
        """Evaluate ResNet50d wear classification model accuracy"""
        print("\n🔍 Evaluating ResNet50d Wear Classification Model...")
        
        model_path = "weights/resnet50d_5epochs_accuracy0.80357_weights.pth"
        
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            return {"error": "Model file not found", "accuracy": 0.0}
        
        try:
            import timm
            from models.particle_analyzer import ParticleAnalyzer
            
            # Initialize analyzer
            analyzer = ParticleAnalyzer(cnn_model_path=model_path)
            
            # Extract accuracy from filename
            filename_accuracy = 0.80357  # From filename
            
            # Model architecture info
            model_info = {
                "model_type": "ResNet50d",
                "num_classes": 3,
                "classes": ["Early Wear", "Middle Wear", "Late Wear"],
                "training_epochs": 5,
                "reported_accuracy": filename_accuracy,
                "model_loaded": analyzer.cnn_model is not None
            }
            
            if analyzer.cnn_model is not None:
                # Count parameters
                total_params = sum(p.numel() for p in analyzer.cnn_model.parameters())
                trainable_params = sum(p.numel() for p in analyzer.cnn_model.parameters() if p.requires_grad)
                
                model_info.update({
                    "total_parameters": total_params,
                    "trainable_parameters": trainable_params,
                    "model_size_mb": total_params * 4 / (1024 * 1024),  # Assuming float32
                })
                
                print(f"✅ ResNet50d Model Loaded Successfully")
                print(f"   📊 Reported Accuracy: {filename_accuracy:.5f} ({filename_accuracy*100:.3f}%)")
                print(f"   🔢 Total Parameters: {total_params:,}")
                print(f"   💾 Model Size: {model_info['model_size_mb']:.2f} MB")
            else:
                print(f"❌ Failed to load ResNet50d model")
                
            return model_info
            
        except Exception as e:
            print(f"❌ Error evaluating ResNet50d: {e}")
            return {"error": str(e), "accuracy": 0.0}
    
    def evaluate_transformer_metrics(self) -> Dict:
        """Evaluate Transformer-based detection model metrics (replaces YOLOv3)"""
        print("\n🔍 Evaluating Transformer Detection Model...")

        try:
            from models.transformer_detector import TransformerParticleDetector

            # Initialize transformer detector (DETR by default)
            detector = TransformerParticleDetector(
                model_name="facebook/detr-resnet-50",
                confidence_threshold=0.5
            )

            # Get model info
            transformer_info = detector.get_model_info()

            if transformer_info.get("simulation_mode", False):
                print(f"❌ Transformer model not loaded - running in simulation mode")
                return transformer_info

            print(f"✅ Transformer Model Loaded Successfully")
            print(f"   🤖 Model Type: {transformer_info.get('model_type', 'Unknown')}")
            print(f"   🔢 Total Parameters: {transformer_info.get('total_parameters', 0):,}")
            print(f"   💾 Model Size: {transformer_info.get('model_size_mb', 0):.2f} MB")
            print(f"   🎯 Confidence Threshold: {transformer_info.get('confidence_threshold', 0.5)}")
            print(f"   🏗️ Architecture: {transformer_info.get('architecture', 'Unknown')}")

            return transformer_info

        except Exception as e:
            print(f"❌ Error evaluating Transformer model: {e}")
            return {"error": str(e), "model_loaded": False, "simulation_mode": True}
    
    def evaluate_rt_detr_performance(self) -> Dict:
        """Evaluate RT-DETR model performance"""
        print("\n🔍 Evaluating RT-DETR Model Performance...")
        
        try:
            from models.rt_detr_detector import RTDETRParticleDetector
            
            # Initialize detector
            detector = RTDETRParticleDetector()
            
            rt_detr_info = {
                "model_type": "RT-DETR",
                "backbone": "ResNet50",
                "model_loaded": detector.model is not None,
                "confidence_threshold": detector.confidence_threshold,
                "particle_classes": detector.class_names
            }
            
            if detector.model is not None:
                # Count parameters
                total_params = sum(p.numel() for p in detector.model.parameters())
                rt_detr_info.update({
                    "total_parameters": total_params,
                    "model_size_mb": total_params * 4 / (1024 * 1024),
                })
                
                print(f"✅ RT-DETR Model Loaded Successfully")
                print(f"   🔢 Total Parameters: {total_params:,}")
                print(f"   💾 Model Size: {rt_detr_info['model_size_mb']:.2f} MB")
                print(f"   🎯 Confidence Threshold: {detector.confidence_threshold}")
            else:
                print(f"⚠️  RT-DETR running in simulation mode")
                rt_detr_info["simulation_mode"] = True
                
            return rt_detr_info
            
        except Exception as e:
            print(f"❌ Error evaluating RT-DETR: {e}")
            return {"error": str(e)}
    
    def benchmark_detection_speed(self) -> Dict:
        """Benchmark detection speed for different methods"""
        print("\n⏱️  Benchmarking Detection Speed...")
        
        # Create test image
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        speed_results = {}
        
        # Test RT-DETR speed
        try:
            from models.rt_detr_detector import RTDETRParticleDetector
            detector = RTDETRParticleDetector()
            
            start_time = time.time()
            detections = detector.detect_particles(test_image)
            rt_detr_time = time.time() - start_time
            
            speed_results["RT-DETR"] = {
                "time_seconds": rt_detr_time,
                "fps": 1.0 / rt_detr_time if rt_detr_time > 0 else 0,
                "detections_count": len(detections)
            }
            
            print(f"   🚀 RT-DETR: {rt_detr_time:.3f}s ({1.0/rt_detr_time:.1f} FPS)")
            
        except Exception as e:
            print(f"   ❌ RT-DETR speed test failed: {e}")
        
        # Test Practical Detector speed
        try:
            from models.practical_detector import PracticalParticleDetector
            detector = PracticalParticleDetector()
            
            # Save test image temporarily
            test_path = "temp_test_image.jpg"
            cv2.imwrite(test_path, test_image)
            
            start_time = time.time()
            result = detector.detect_particles(test_path)
            practical_time = time.time() - start_time
            
            speed_results["Practical_CV"] = {
                "time_seconds": practical_time,
                "fps": 1.0 / practical_time if practical_time > 0 else 0,
                "detections_count": result.get('total_count', 0)
            }
            
            print(f"   🔧 Practical CV: {practical_time:.3f}s ({1.0/practical_time:.1f} FPS)")
            
            # Clean up
            if os.path.exists(test_path):
                os.remove(test_path)
                
        except Exception as e:
            print(f"   ❌ Practical detector speed test failed: {e}")
        
        return speed_results
    
    def calculate_detection_metrics(self, ground_truth: List, predictions: List) -> Dict:
        """Calculate detection performance metrics"""
        print("\n📊 Calculating Detection Metrics...")
        
        if len(ground_truth) == 0 or len(predictions) == 0:
            return {"error": "No ground truth or predictions available"}
        
        # Convert to binary classification (detected vs not detected)
        gt_binary = [1 if len(gt) > 0 else 0 for gt in ground_truth]
        pred_binary = [1 if len(pred) > 0 else 0 for pred in predictions]
        
        metrics = {
            "precision": precision_score(gt_binary, pred_binary, zero_division=0),
            "recall": recall_score(gt_binary, pred_binary, zero_division=0),
            "f1_score": f1_score(gt_binary, pred_binary, zero_division=0),
            "accuracy": sum(1 for gt, pred in zip(gt_binary, pred_binary) if gt == pred) / len(gt_binary)
        }
        
        print(f"   🎯 Precision: {metrics['precision']:.3f}")
        print(f"   🔍 Recall: {metrics['recall']:.3f}")
        print(f"   ⚖️  F1-Score: {metrics['f1_score']:.3f}")
        print(f"   ✅ Accuracy: {metrics['accuracy']:.3f}")
        
        return metrics

    def test_wear_classification_accuracy(self) -> Dict:
        """Test wear classification accuracy with sample data"""
        print("\n🧪 Testing Wear Classification Accuracy...")

        try:
            from models.particle_analyzer import ParticleAnalyzer

            analyzer = ParticleAnalyzer(cnn_model_path="weights/resnet50d_5epochs_accuracy0.80357_weights.pth")

            if analyzer.cnn_model is None:
                return {"error": "CNN model not loaded"}

            # Create test images for different wear stages
            test_results = []

            for wear_stage in ["early", "middle", "late"]:
                # Create synthetic test image
                if wear_stage == "early":
                    # Few small particles
                    test_img = np.ones((224, 224, 3), dtype=np.uint8) * 200
                    for _ in range(5):
                        x, y = np.random.randint(20, 200, 2)
                        cv2.circle(test_img, (x, y), np.random.randint(2, 5), (100, 100, 100), -1)

                elif wear_stage == "middle":
                    # Moderate particles
                    test_img = np.ones((224, 224, 3), dtype=np.uint8) * 180
                    for _ in range(15):
                        x, y = np.random.randint(20, 200, 2)
                        cv2.circle(test_img, (x, y), np.random.randint(3, 8), (80, 80, 80), -1)

                else:  # late
                    # Many large particles
                    test_img = np.ones((224, 224, 3), dtype=np.uint8) * 150
                    for _ in range(30):
                        x, y = np.random.randint(20, 200, 2)
                        cv2.circle(test_img, (x, y), np.random.randint(5, 12), (60, 60, 60), -1)

                # Classify wear stage
                classification = analyzer._cnn_wear_classification(test_img)
                test_results.append({
                    "expected": wear_stage,
                    "predicted": classification.get("wear_stage", "Unknown"),
                    "confidence": classification.get("confidence", 0.0)
                })

                print(f"   {wear_stage.title()} Wear Test: {classification.get('wear_stage', 'Unknown')} "
                      f"(confidence: {classification.get('confidence', 0.0):.3f})")

            return {"test_results": test_results, "model_available": True}

        except Exception as e:
            print(f"   ❌ Wear classification test failed: {e}")
            return {"error": str(e)}

    def analyze_loss_functions(self) -> Dict:
        """Analyze loss functions used in the models"""
        print("\n📉 Analyzing Loss Functions...")

        loss_analysis = {}

        # Transformer Detection Loss Analysis
        try:
            loss_analysis["Transformer_Detection"] = {
                "loss_components": [
                    "Classification Loss: Focal/Cross Entropy for object classification",
                    "Regression Loss: L1 Loss for bounding box coordinates",
                    "Hungarian Matching: Optimal assignment between predictions and targets"
                ],
                "loss_functions": {
                    "classification": "Focal Loss / Cross Entropy",
                    "regression": "L1 Loss (smooth)",
                    "matching": "Hungarian algorithm for bipartite matching"
                },
                "total_loss": "weighted_sum(class_loss + bbox_loss + giou_loss)",
                "advantages": [
                    "End-to-end differentiable",
                    "No anchor boxes required",
                    "Global optimization via set prediction"
                ]
            }

            print("   ✅ Transformer Detection Loss Components:")
            print("      - Classification: Focal/Cross Entropy for object detection")
            print("      - Regression: L1 Loss for bounding box coordinates")
            print("      - Matching: Hungarian algorithm for optimal assignment")

        except Exception as e:
            print(f"   ❌ Transformer loss analysis failed: {e}")

        # ResNet50d Loss Analysis
        loss_analysis["ResNet50d"] = {
            "loss_function": "Cross Entropy Loss",
            "purpose": "Multi-class wear stage classification",
            "classes": 3,
            "optimization": "Likely Adam or SGD optimizer"
        }

        print("   ✅ ResNet50d Loss:")
        print("      - Cross Entropy Loss for 3-class wear classification")

        # RT-DETR Loss Analysis
        loss_analysis["RT-DETR"] = {
            "loss_components": [
                "Classification Loss: Focal Loss or Cross Entropy",
                "Regression Loss: L1 Loss for bounding boxes",
                "Hungarian Matching: Bipartite matching loss"
            ],
            "architecture": "Transformer-based with DETR-style losses"
        }

        print("   ✅ RT-DETR Loss (Estimated):")
        print("      - Classification: Focal/Cross Entropy Loss")
        print("      - Regression: L1 Loss for bounding boxes")
        print("      - Matching: Hungarian algorithm for assignment")

        return loss_analysis

    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive model evaluation report"""
        print("\n📋 Generating Comprehensive Model Report...")

        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "system_info": {
                "python_version": sys.version,
                "pytorch_version": torch.__version__ if 'torch' in sys.modules else "Not available",
                "cuda_available": torch.cuda.is_available() if 'torch' in sys.modules else False,
                "device": str(self.device)
            }
        }

        # Run all evaluations
        report["resnet50d_evaluation"] = self.evaluate_resnet50d_accuracy()
        report["transformer_evaluation"] = self.evaluate_transformer_metrics()
        report["rt_detr_evaluation"] = self.evaluate_rt_detr_performance()
        report["speed_benchmark"] = self.benchmark_detection_speed()
        report["wear_classification_test"] = self.test_wear_classification_accuracy()
        report["loss_analysis"] = self.analyze_loss_functions()

        # Save report
        report_path = "model_evaluation_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        print(f"\n💾 Report saved to: {report_path}")

        return report

def main():
    """Main evaluation function"""
    print("=" * 80)
    print("🔬 COMPREHENSIVE MODEL ACCURACY & LOSS EVALUATION")
    print("   Oil Particle Detection System")
    print("=" * 80)

    evaluator = ModelEvaluator()

    try:
        # Generate comprehensive report
        report = evaluator.generate_comprehensive_report()

        # Print summary
        print("\n" + "=" * 80)
        print("📊 EVALUATION SUMMARY")
        print("=" * 80)

        # ResNet50d Summary
        resnet_info = report.get("resnet50d_evaluation", {})
        if "reported_accuracy" in resnet_info:
            print(f"🧠 ResNet50d Wear Classification:")
            print(f"   Accuracy: {resnet_info['reported_accuracy']:.5f} ({resnet_info['reported_accuracy']*100:.3f}%)")
            print(f"   Parameters: {resnet_info.get('total_parameters', 'Unknown'):,}")
            print(f"   Model Size: {resnet_info.get('model_size_mb', 0):.2f} MB")

        # Transformer Detection Summary
        transformer_info = report.get("transformer_evaluation", {})
        if "model_loaded" in transformer_info and transformer_info["model_loaded"]:
            print(f"\n🤖 Transformer Detection:")
            print(f"   Model Type: {transformer_info.get('model_type', 'Unknown')}")
            print(f"   Parameters: {transformer_info.get('total_parameters', 'Unknown'):,}")
            print(f"   Model Size: {transformer_info.get('model_size_mb', 0):.2f} MB")
            print(f"   Architecture: {transformer_info.get('architecture', 'Unknown')}")

        # RT-DETR Summary
        rt_detr_info = report.get("rt_detr_evaluation", {})
        if rt_detr_info.get("model_loaded", False):
            print(f"\n🚀 RT-DETR Detection:")
            print(f"   Parameters: {rt_detr_info.get('total_parameters', 'Unknown'):,}")
            print(f"   Model Size: {rt_detr_info.get('model_size_mb', 0):.2f} MB")
            print(f"   Confidence Threshold: {rt_detr_info.get('confidence_threshold', 0.5)}")
        else:
            print(f"\n⚠️  RT-DETR: Running in simulation mode")

        # Speed Summary
        speed_info = report.get("speed_benchmark", {})
        if speed_info:
            print(f"\n⚡ Performance Benchmark:")
            for method, stats in speed_info.items():
                print(f"   {method}: {stats.get('time_seconds', 0):.3f}s ({stats.get('fps', 0):.1f} FPS)")

        print("\n✅ Evaluation completed successfully!")
        print(f"📄 Detailed report saved to: model_evaluation_report.json")

    except Exception as e:
        print(f"\n❌ Evaluation failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
