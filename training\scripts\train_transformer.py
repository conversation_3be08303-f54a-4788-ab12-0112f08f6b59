"""
Advanced Training Pipeline for Oil Particle Detection
Supports DETR, DINO, and other transformer-based models
Designed to achieve 90%+ accuracy through fine-tuning
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl
from torch.utils.data import DataLoader
from transformers import (
    DetrForObjectDetection, 
    DetrImageProcessor,
    AutoModelForObjectDetection,
    AutoImageProcessor
)
import wandb
from typing import Dict, List, Optional, Tuple
import yaml
import argparse
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ParticleDetectionTrainer(pl.LightningModule):
    """
    PyTorch Lightning trainer for transformer-based particle detection
    Supports multiple architectures and advanced training techniques
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
        self.save_hyperparameters(config)
        
        # Model components
        self.model = None
        self.processor = None
        self.criterion = None
        
        # Training metrics
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        
        # Initialize model and loss
        self._setup_model()
        self._setup_loss()
        
    def _setup_model(self):
        """Initialize transformer model and processor"""
        model_name = self.config['model']['name']
        
        try:
            logger.info(f"Loading model: {model_name}")
            
            # Load processor
            self.processor = AutoImageProcessor.from_pretrained(model_name)
            
            # Load model
            self.model = AutoModelForObjectDetection.from_pretrained(
                model_name,
                num_labels=self.config['model']['num_classes'],
                ignore_mismatched_sizes=True
            )
            
            # Freeze backbone if specified
            if self.config['training'].get('freeze_backbone', False):
                self._freeze_backbone()
                
            logger.info(f"Model loaded successfully: {model_name}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def _setup_loss(self):
        """Setup custom loss function"""
        loss_config = self.config['loss']
        
        if loss_config['type'] == 'detr_loss':
            # Use DETR's built-in loss
            self.criterion = None  # Model handles loss internally
        elif loss_config['type'] == 'custom_particle_loss':
            from losses.particle_loss import ParticleDetectionLoss
            self.criterion = ParticleDetectionLoss(
                focal_alpha=loss_config.get('focal_alpha', 0.25),
                focal_gamma=loss_config.get('focal_gamma', 2.0),
                iou_loss_weight=loss_config.get('iou_weight', 1.0)
            )
        else:
            raise ValueError(f"Unknown loss type: {loss_config['type']}")
    
    def _freeze_backbone(self):
        """Freeze backbone parameters for transfer learning"""
        if hasattr(self.model, 'model') and hasattr(self.model.model, 'backbone'):
            for param in self.model.model.backbone.parameters():
                param.requires_grad = False
            logger.info("Backbone frozen for transfer learning")
    
    def forward(self, pixel_values, labels=None):
        """Forward pass"""
        if labels is not None:
            # Training mode
            outputs = self.model(pixel_values=pixel_values, labels=labels)
            return outputs
        else:
            # Inference mode
            outputs = self.model(pixel_values=pixel_values)
            return outputs
    
    def training_step(self, batch, batch_idx):
        """Training step"""
        pixel_values, labels = batch
        
        # Forward pass
        outputs = self.forward(pixel_values, labels)
        
        # Calculate loss
        if self.criterion is None:
            # Use model's built-in loss
            loss = outputs.loss
        else:
            # Use custom loss
            loss = self.criterion(outputs, labels)
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('learning_rate', self.trainer.optimizers[0].param_groups[0]['lr'])
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        pixel_values, labels = batch
        
        # Forward pass
        outputs = self.forward(pixel_values, labels)
        
        # Calculate loss
        if self.criterion is None:
            loss = outputs.loss
        else:
            loss = self.criterion(outputs, labels)
        
        # Log metrics
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        
        return loss
    
    def configure_optimizers(self):
        """Configure optimizer and scheduler"""
        optimizer_config = self.config['optimizer']
        
        # Setup optimizer
        if optimizer_config['type'] == 'adamw':
            optimizer = torch.optim.AdamW(
                self.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config.get('weight_decay', 0.01),
                betas=optimizer_config.get('betas', (0.9, 0.999))
            )
        elif optimizer_config['type'] == 'sgd':
            optimizer = torch.optim.SGD(
                self.parameters(),
                lr=optimizer_config['lr'],
                momentum=optimizer_config.get('momentum', 0.9),
                weight_decay=optimizer_config.get('weight_decay', 1e-4)
            )
        else:
            raise ValueError(f"Unknown optimizer: {optimizer_config['type']}")
        
        # Setup scheduler
        scheduler_config = self.config.get('scheduler', {})
        if scheduler_config.get('type') == 'cosine':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=scheduler_config.get('T_max', 100),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
            return [optimizer], [scheduler]
        elif scheduler_config.get('type') == 'step':
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
            return [optimizer], [scheduler]
        
        return optimizer
    
    def on_validation_epoch_end(self):
        """Called at the end of validation epoch"""
        val_loss = self.trainer.callback_metrics.get('val_loss')
        if val_loss is not None and val_loss < self.best_val_loss:
            self.best_val_loss = val_loss
            logger.info(f"New best validation loss: {val_loss:.4f}")


def load_config(config_path: str) -> Dict:
    """Load training configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def setup_data_loaders(config: Dict) -> Tuple[DataLoader, DataLoader]:
    """Setup training and validation data loaders"""
    from datasets.particle_dataset import ParticleDetectionDataset
    
    # Training dataset
    train_dataset = ParticleDetectionDataset(
        data_dir=config['data']['train_dir'],
        processor=None,  # Will be set by trainer
        augment=config['data'].get('augment', True),
        max_size=config['data'].get('max_size', 800)
    )
    
    # Validation dataset
    val_dataset = ParticleDetectionDataset(
        data_dir=config['data']['val_dir'],
        processor=None,
        augment=False,
        max_size=config['data'].get('max_size', 800)
    )
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=True,
        num_workers=config['training'].get('num_workers', 4),
        collate_fn=train_dataset.collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        num_workers=config['training'].get('num_workers', 4),
        collate_fn=val_dataset.collate_fn
    )
    
    return train_loader, val_loader


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train transformer-based particle detector')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--resume', type=str, help='Path to checkpoint to resume from')
    parser.add_argument('--gpus', type=int, default=1, help='Number of GPUs to use')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Initialize wandb for experiment tracking
    if config.get('wandb', {}).get('enabled', False):
        wandb.init(
            project=config['wandb']['project'],
            name=config['wandb'].get('run_name'),
            config=config
        )
    
    # Setup data loaders
    train_loader, val_loader = setup_data_loaders(config)
    
    # Initialize trainer
    model = ParticleDetectionTrainer(config)
    
    # Setup PyTorch Lightning trainer
    trainer = pl.Trainer(
        max_epochs=config['training']['max_epochs'],
        gpus=args.gpus,
        precision=config['training'].get('precision', 32),
        gradient_clip_val=config['training'].get('gradient_clip_val', 1.0),
        accumulate_grad_batches=config['training'].get('accumulate_grad_batches', 1),
        val_check_interval=config['training'].get('val_check_interval', 1.0),
        callbacks=[
            pl.callbacks.ModelCheckpoint(
                monitor='val_loss',
                mode='min',
                save_top_k=3,
                filename='{epoch}-{val_loss:.4f}'
            ),
            pl.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=config['training'].get('early_stopping_patience', 10),
                mode='min'
            ),
            pl.callbacks.LearningRateMonitor(logging_interval='step')
        ],
        logger=pl.loggers.WandbLogger() if config.get('wandb', {}).get('enabled', False) else True
    )
    
    # Start training
    if args.resume:
        trainer.fit(model, train_loader, val_loader, ckpt_path=args.resume)
    else:
        trainer.fit(model, train_loader, val_loader)
    
    logger.info("Training completed!")


if __name__ == '__main__':
    main()
