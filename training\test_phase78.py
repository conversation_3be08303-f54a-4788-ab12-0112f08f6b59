"""
Test script for Phase 7-8 Advanced Ensemble Methods
Validates that all components are working correctly
"""

import sys
import os
import yaml
import torch
import numpy as np
from pathlib import Path

# Add training directory to path
sys.path.append(str(Path(__file__).parent))

def test_ensemble_methods():
    """Test ensemble methods implementation"""
    print("🧪 Testing Ensemble Methods...")
    
    try:
        from ensemble.ensemble_methods import ModelEnsemble, create_ensemble_config, PARTICLE_ENSEMBLE_CONFIGS
        
        # Test ensemble configuration creation
        config = create_ensemble_config(
            base_models=['facebook/detr-resnet-50', 'facebook/detr-resnet-101'],
            weights=[1.0, 1.2]
        )
        
        print(f"✅ Ensemble config created: {len(config)} models")
        
        # Test predefined configurations
        for name, config in PARTICLE_ENSEMBLE_CONFIGS.items():
            print(f"✅ Found ensemble config: {name} with {len(config)} models")
        
        print("✅ Ensemble methods test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Ensemble methods test failed: {e}")
        return False

def test_advanced_augmentations():
    """Test advanced augmentation pipeline"""
    print("\n🧪 Testing Advanced Augmentations...")
    
    try:
        from augmentations.particle_augmentations import (
            get_advanced_particle_augmentations,
            get_test_time_augmentations,
            ParticleSpecificAugmentation,
            SyntheticParticleGeneration,
            BackgroundSubstitution
        )
        
        # Test advanced augmentations
        transforms = get_advanced_particle_augmentations(image_size=800, training=True)
        print(f"✅ Advanced augmentations created: {len(transforms)} transforms")
        
        # Test TTA augmentations
        tta_transforms = get_test_time_augmentations()
        print(f"✅ TTA augmentations created: {len(tta_transforms)} transforms")
        
        # Test custom augmentation classes
        particle_aug = ParticleSpecificAugmentation(p=0.5)
        synthetic_aug = SyntheticParticleGeneration(p=0.3)
        background_aug = BackgroundSubstitution(p=0.4)
        
        print("✅ Custom augmentation classes instantiated successfully")
        
        # Test with dummy image
        dummy_image = np.random.randint(0, 255, (800, 800, 3), dtype=np.uint8)
        
        try:
            result = particle_aug(image=dummy_image)
            print("✅ ParticleSpecificAugmentation works")
        except Exception as e:
            print(f"⚠️ ParticleSpecificAugmentation warning: {e}")
        
        print("✅ Advanced augmentations test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced augmentations test failed: {e}")
        return False

def test_dataset_conversion():
    """Test dataset conversion results"""
    print("\n🧪 Testing Dataset Conversion...")
    
    try:
        # Check if converted dataset exists
        train_annotations = Path("data/particle_coco/annotations_train.json")
        val_annotations = Path("data/particle_coco/annotations_val.json")
        
        if train_annotations.exists() and val_annotations.exists():
            print("✅ COCO format dataset files found")
            
            # Load and validate annotations
            import json
            
            with open(train_annotations, 'r') as f:
                train_data = json.load(f)
            
            with open(val_annotations, 'r') as f:
                val_data = json.load(f)
            
            print(f"✅ Training set: {len(train_data['images'])} images, {len(train_data['annotations'])} annotations")
            print(f"✅ Validation set: {len(val_data['images'])} images, {len(val_data['annotations'])} annotations")
            print(f"✅ Categories: {len(train_data['categories'])} classes")
            
            # Validate structure
            required_keys = ['images', 'annotations', 'categories']
            for key in required_keys:
                if key not in train_data:
                    raise ValueError(f"Missing key: {key}")
            
            print("✅ Dataset conversion test passed!")
            return True
        else:
            print("❌ COCO format dataset files not found")
            return False
            
    except Exception as e:
        print(f"❌ Dataset conversion test failed: {e}")
        return False

def test_evaluator_enhancements():
    """Test enhanced evaluator with TTA support"""
    print("\n🧪 Testing Enhanced Evaluator...")
    
    try:
        from utils.evaluator import ParticleDetectionEvaluator
        
        # Create evaluator
        evaluator = ParticleDetectionEvaluator(
            iou_thresholds=[0.5, 0.75, 0.9],
            confidence_threshold=0.5
        )
        
        print("✅ Enhanced evaluator created")
        
        # Test methods exist
        methods_to_test = [
            'evaluate_model_with_tta',
            'evaluate_ensemble',
            'reset'
        ]
        
        for method in methods_to_test:
            if hasattr(evaluator, method):
                print(f"✅ Method {method} available")
            else:
                print(f"❌ Method {method} missing")
                return False
        
        print("✅ Enhanced evaluator test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced evaluator test failed: {e}")
        return False

def test_configuration():
    """Test configuration files"""
    print("\n🧪 Testing Configuration...")
    
    try:
        config_path = Path("training/config/detr_config.yaml")
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            print("✅ Configuration file loaded")
            
            # Check key sections
            required_sections = ['data', 'model', 'training']
            for section in required_sections:
                if section in config:
                    print(f"✅ Section '{section}' found")
                else:
                    print(f"❌ Section '{section}' missing")
                    return False
            
            # Check data paths point to COCO format
            if 'train_annotations' in config['data']:
                train_path = config['data']['train_annotations']
                if 'coco' in train_path.lower():
                    print("✅ Configuration uses COCO format dataset")
                else:
                    print("⚠️ Configuration may not use COCO format dataset")
            
            print("✅ Configuration test passed!")
            return True
        else:
            print("❌ Configuration file not found")
            return False
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_training_infrastructure():
    """Test training infrastructure components"""
    print("\n🧪 Testing Training Infrastructure...")
    
    try:
        # Test PyTorch Lightning availability
        import pytorch_lightning as pl
        print(f"✅ PyTorch Lightning {pl.__version__} available")
        
        # Test Weights & Biases availability
        import wandb
        print(f"✅ Weights & Biases {wandb.__version__} available")
        
        # Test transformers library
        from transformers import AutoModelForObjectDetection, AutoImageProcessor
        print("✅ Transformers library available")
        
        # Test CUDA availability
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
            print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        else:
            print("⚠️ CUDA not available, will use CPU")
        
        print("✅ Training infrastructure test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Training infrastructure test failed: {e}")
        return False

def main():
    """Run all Phase 7-8 tests"""
    print("🚀 PHASE 7-8 ADVANCED ENSEMBLE METHODS - SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        test_dataset_conversion,
        test_advanced_augmentations,
        test_ensemble_methods,
        test_evaluator_enhancements,
        test_configuration,
        test_training_infrastructure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Phase 7-8 implementation is ready!")
        print("\n🎯 NEXT STEPS:")
        print("1. Run: python training/train_advanced.py --target-accuracy 90")
        print("2. Monitor training progress in Weights & Biases")
        print("3. Evaluate ensemble performance on validation set")
        return 0
    else:
        print(f"❌ {total - passed} tests failed. Please fix issues before proceeding.")
        return 1

if __name__ == '__main__':
    exit(main())
