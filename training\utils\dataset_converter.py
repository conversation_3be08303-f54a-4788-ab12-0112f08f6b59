"""
Dataset Conversion Utilities for Oil Particle Detection
Converts existing YOLO format data to COCO format for transformer training
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime
import shutil

logger = logging.getLogger(__name__)


class YOLOtoCOCOConverter:
    """
    Convert YOLO format annotations to COCO format for transformer training
    """
    
    def __init__(self, 
                 yolo_data_dir: str,
                 output_dir: str,
                 class_names: Optional[List[str]] = None):
        """
        Args:
            yolo_data_dir: Directory containing YOLO format data
            output_dir: Output directory for COCO format data
            class_names: List of class names (if None, will read from classes.names)
        """
        self.yolo_data_dir = Path(yolo_data_dir)
        self.output_dir = Path(output_dir)
        self.class_names = class_names
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / 'images').mkdir(exist_ok=True)
        
        # Load class names
        if self.class_names is None:
            self._load_class_names()
        
        # COCO format structure
        self.coco_data = {
            "info": {
                "description": "Oil Particle Detection Dataset",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "Oil Particle Detection System",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [],
            "images": [],
            "annotations": [],
            "categories": []
        }
        
        # Initialize categories
        self._setup_categories()
        
        # Counters
        self.image_id = 1
        self.annotation_id = 1
    
    def _load_class_names(self):
        """Load class names from classes.names file"""
        classes_file = self.yolo_data_dir / 'classes.names'
        if classes_file.exists():
            with open(classes_file, 'r') as f:
                self.class_names = [line.strip() for line in f.readlines()]
        else:
            # Default particle classes
            self.class_names = [
                'spherical_particle',
                'irregular_particle', 
                'elongated_particle',
                'cluster_particle',
                'micro_particle'
            ]
        
        logger.info(f"Loaded {len(self.class_names)} classes: {self.class_names}")
    
    def _setup_categories(self):
        """Setup COCO categories"""
        for i, class_name in enumerate(self.class_names):
            category = {
                "id": i + 1,  # COCO categories start from 1
                "name": class_name,
                "supercategory": "particle"
            }
            self.coco_data["categories"].append(category)
    
    def convert_dataset(self, split_name: str = 'train') -> str:
        """
        Convert YOLO dataset to COCO format
        
        Args:
            split_name: Dataset split name ('train', 'val', 'test')
            
        Returns:
            Path to generated annotations file
        """
        logger.info(f"Converting {split_name} dataset...")
        
        # Find images and labels
        images_dir = self.yolo_data_dir / 'images'
        labels_dir = self.yolo_data_dir / 'labels'

        # Handle Windows path separators
        if not images_dir.exists():
            logger.error(f"Images directory not found: {images_dir}")
            logger.info(f"Looking for images in: {self.yolo_data_dir}")
            logger.info(f"Contents: {list(self.yolo_data_dir.iterdir())}")
            return None

        if not labels_dir.exists():
            logger.error(f"Labels directory not found: {labels_dir}")
            return None
        
        # Get image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(images_dir.glob(f'*{ext}')))
            image_files.extend(list(images_dir.glob(f'*{ext.upper()}')))
        
        logger.info(f"Found {len(image_files)} images")
        
        # Process each image
        processed_count = 0
        for image_file in image_files:
            if self._process_image(image_file, labels_dir):
                processed_count += 1
        
        logger.info(f"Processed {processed_count} images with annotations")
        
        # Save COCO annotations
        annotations_file = self.output_dir / f'annotations_{split_name}.json'
        with open(annotations_file, 'w') as f:
            json.dump(self.coco_data, f, indent=2)
        
        logger.info(f"Saved annotations to: {annotations_file}")
        return str(annotations_file)
    
    def _process_image(self, image_file: Path, labels_dir: Path) -> bool:
        """Process a single image and its annotations"""
        # Load image to get dimensions
        image = cv2.imread(str(image_file))
        if image is None:
            logger.warning(f"Could not load image: {image_file}")
            return False
        
        height, width = image.shape[:2]
        
        # Copy image to output directory
        output_image_path = self.output_dir / 'images' / image_file.name
        shutil.copy2(image_file, output_image_path)
        
        # Add image info to COCO data
        image_info = {
            "id": self.image_id,
            "width": width,
            "height": height,
            "file_name": image_file.name,
            "license": 0,
            "flickr_url": "",
            "coco_url": "",
            "date_captured": ""
        }
        self.coco_data["images"].append(image_info)
        
        # Look for corresponding label file
        label_file = labels_dir / f"{image_file.stem}.txt"
        if not label_file.exists():
            logger.debug(f"No label file found for: {image_file.name}")
            self.image_id += 1
            return True
        
        # Process annotations
        annotations_added = 0
        with open(label_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) < 5:
                    continue
                
                try:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    bbox_width = float(parts[3])
                    bbox_height = float(parts[4])
                    
                    # Convert YOLO format to COCO format
                    annotation = self._yolo_to_coco_annotation(
                        class_id, x_center, y_center, bbox_width, bbox_height,
                        width, height, self.image_id
                    )
                    
                    if annotation:
                        self.coco_data["annotations"].append(annotation)
                        self.annotation_id += 1
                        annotations_added += 1
                
                except (ValueError, IndexError) as e:
                    logger.warning(f"Invalid annotation in {label_file}: {line}")
                    continue
        
        self.image_id += 1
        return annotations_added > 0
    
    def _yolo_to_coco_annotation(self, class_id: int, x_center: float, y_center: float,
                                bbox_width: float, bbox_height: float,
                                img_width: int, img_height: int, image_id: int) -> Optional[Dict]:
        """Convert YOLO annotation to COCO format"""
        # Convert normalized coordinates to absolute
        abs_x_center = x_center * img_width
        abs_y_center = y_center * img_height
        abs_width = bbox_width * img_width
        abs_height = bbox_height * img_height
        
        # Convert center format to top-left format
        x_min = abs_x_center - abs_width / 2
        y_min = abs_y_center - abs_height / 2
        
        # Ensure coordinates are within image bounds
        x_min = max(0, x_min)
        y_min = max(0, y_min)
        abs_width = min(abs_width, img_width - x_min)
        abs_height = min(abs_height, img_height - y_min)
        
        # Skip invalid boxes
        if abs_width <= 0 or abs_height <= 0:
            return None
        
        # Create COCO annotation
        annotation = {
            "id": self.annotation_id,
            "image_id": image_id,
            "category_id": class_id + 1,  # COCO categories start from 1
            "segmentation": [],
            "area": abs_width * abs_height,
            "bbox": [x_min, y_min, abs_width, abs_height],
            "iscrowd": 0,
            "attributes": {
                "occluded": False,
                "truncated": False,
                "difficult": False
            }
        }
        
        return annotation
    
    def create_train_val_split(self, train_ratio: float = 0.8) -> Tuple[str, str]:
        """
        Create train/validation split from converted data
        
        Args:
            train_ratio: Ratio of data to use for training
            
        Returns:
            Tuple of (train_annotations_path, val_annotations_path)
        """
        if not self.coco_data["images"]:
            logger.error("No images found. Run convert_dataset first.")
            return None, None
        
        # Shuffle images
        images = self.coco_data["images"].copy()
        np.random.shuffle(images)
        
        # Split images
        split_idx = int(len(images) * train_ratio)
        train_images = images[:split_idx]
        val_images = images[split_idx:]
        
        # Create train dataset
        train_image_ids = {img["id"] for img in train_images}
        train_annotations = [ann for ann in self.coco_data["annotations"] 
                           if ann["image_id"] in train_image_ids]
        
        train_data = self.coco_data.copy()
        train_data["images"] = train_images
        train_data["annotations"] = train_annotations
        
        # Create validation dataset
        val_image_ids = {img["id"] for img in val_images}
        val_annotations = [ann for ann in self.coco_data["annotations"] 
                         if ann["image_id"] in val_image_ids]
        
        val_data = self.coco_data.copy()
        val_data["images"] = val_images
        val_data["annotations"] = val_annotations
        
        # Save datasets
        train_file = self.output_dir / 'annotations_train.json'
        val_file = self.output_dir / 'annotations_val.json'
        
        with open(train_file, 'w') as f:
            json.dump(train_data, f, indent=2)
        
        with open(val_file, 'w') as f:
            json.dump(val_data, f, indent=2)
        
        logger.info(f"Created train split: {len(train_images)} images, {len(train_annotations)} annotations")
        logger.info(f"Created val split: {len(val_images)} images, {len(val_annotations)} annotations")
        
        return str(train_file), str(val_file)


def convert_custom_dataset():
    """Convert the custom particle dataset to COCO format"""
    converter = YOLOtoCOCOConverter(
        yolo_data_dir='data/custom',
        output_dir='data/particle_coco'
    )
    
    # Convert dataset
    converter.convert_dataset('full')
    
    # Create train/val split
    train_file, val_file = converter.create_train_val_split(train_ratio=0.8)
    
    return train_file, val_file


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    train_file, val_file = convert_custom_dataset()
    print(f"Dataset conversion completed!")
    print(f"Train annotations: {train_file}")
    print(f"Validation annotations: {val_file}")
