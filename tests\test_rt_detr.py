#!/usr/bin/env python3
"""
Test script to verify RT-DETR dependencies are working
"""

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing RT-DETR dependencies...")
    
    try:
        import transformers
        print(f"✓ transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"✗ transformers: {e}")
        return False
    
    try:
        import torch
        print(f"✓ torch: {torch.__version__}")
    except ImportError as e:
        print(f"✗ torch: {e}")
        return False
    
    try:
        import torchvision
        print(f"✓ torchvision: {torchvision.__version__}")
    except ImportError as e:
        print(f"✗ torchvision: {e}")
        return False
    
    try:
        import ultralytics
        print(f"✓ ultralytics: {ultralytics.__version__}")
    except ImportError as e:
        print(f"✗ ultralytics: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ opencv-python: {cv2.__version__}")
    except ImportError as e:
        print(f"✗ opencv-python: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ numpy: {np.__version__}")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    return True

def test_rt_detr_detector():
    """Test if RT-DETR detector can be initialized"""
    print("\nTesting RT-DETR detector initialization...")
    
    try:
        from models.rt_detr_detector import RTDETRParticleDetector
        detector = RTDETRParticleDetector()
        print("✓ RT-DETR detector initialized successfully")
        return True
    except Exception as e:
        print(f"✗ RT-DETR detector failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("RT-DETR Dependencies Test")
    print("=" * 50)
    
    imports_ok = test_imports()
    
    if imports_ok:
        print("\n" + "=" * 50)
        detector_ok = test_rt_detr_detector()
        
        if detector_ok:
            print("\n🎉 All tests passed! RT-DETR is ready to use.")
        else:
            print("\n⚠️  Dependencies OK, but RT-DETR detector needs fixing.")
    else:
        print("\n❌ Some dependencies are missing. Please install them first.")
    
    print("=" * 50)
