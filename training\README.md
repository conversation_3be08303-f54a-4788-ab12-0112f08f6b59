# 🚀 Advanced Transformer Training for Oil Particle Detection

This training infrastructure is designed to achieve **90%+ accuracy** in oil particle detection using state-of-the-art transformer models.

## 📋 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Prepare Your Dataset
Create the following directory structure:
```
data/
├── train/
│   ├── images/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   └── annotations.json  # COCO format
├── val/
│   ├── images/
│   └── annotations.json
└── test/
    ├── images/
    └── annotations.json
```

### 3. Configure Training
Edit `config/detr_config.yaml` to match your setup:
```yaml
# Update data paths
data:
  train_dir: "data/train"
  val_dir: "data/val"
  test_dir: "data/test"

# Adjust for your GPU
training:
  batch_size: 8  # Reduce if GPU memory is limited
  max_epochs: 100
```

### 4. Start Training
```bash
# Basic training
python train_transformer.py --config config/detr_config.yaml

# With GPU acceleration
python train_transformer.py --config config/detr_config.yaml --gpus 1

# Resume from checkpoint
python train_transformer.py --config config/detr_config.yaml --resume path/to/checkpoint.ckpt
```

## 🎯 Achieving 90% Accuracy

### Phase 1: Foundation (Weeks 1-2)
1. **Create High-Quality Dataset**
   - Collect 5,000+ annotated particle images
   - Ensure diverse particle types, sizes, and conditions
   - Use COCO format annotations

2. **Start with Transfer Learning**
   ```yaml
   training:
     freeze_backbone: true  # Start with frozen backbone
     max_epochs: 20
     lr: 1e-4
   ```

### Phase 2: Fine-tuning (Weeks 3-4)
1. **Unfreeze and Fine-tune**
   ```yaml
   training:
     freeze_backbone: false  # Unfreeze for full training
     max_epochs: 100
     lr: 1e-5  # Lower learning rate
   ```

2. **Advanced Data Augmentation**
   - Geometric transformations
   - Photometric variations
   - Particle-specific augmentations

### Phase 3: Optimization (Weeks 5-6)
1. **Custom Loss Functions**
   ```yaml
   loss:
     type: "custom_particle_loss"
     focal_alpha: 0.25
     focal_gamma: 2.0
     iou_weight: 1.0
   ```

2. **Architecture Experiments**
   - Try different transformer models (DETR, DINO, Conditional DETR)
   - Experiment with backbone networks
   - Optimize for small object detection

## 🔧 Configuration Options

### Model Selection
```yaml
model:
  name: "facebook/detr-resnet-50"        # Standard DETR
  # name: "facebook/detr-resnet-101"     # Larger DETR
  # name: "IDEA-Research/dino-resnet-50" # DINO (better performance)
```

### Training Strategies
```yaml
training:
  # Progressive training
  freeze_backbone: true    # Start frozen
  max_epochs: 20          # Initial epochs
  
  # Then switch to:
  freeze_backbone: false   # Full training
  max_epochs: 100         # Extended training
  lr: 1e-5               # Lower learning rate
```

### Advanced Augmentation
```yaml
augmentation:
  enabled: true
  horizontal_flip: 0.5
  rotation: 15
  brightness: 0.2
  elastic_transform: 0.2
  gaussian_noise: 0.01
```

## 📊 Monitoring Training

### Weights & Biases Integration
```yaml
wandb:
  enabled: true
  project: "oil-particle-detection"
  run_name: "detr-resnet50-v1"
```

### Key Metrics to Watch
- **Training Loss**: Should decrease steadily
- **Validation Loss**: Should follow training loss
- **mAP@0.5**: Target >90%
- **Learning Rate**: Should decay properly

## 🎯 Expected Performance Timeline

| Week | Milestone | Expected mAP@0.5 |
|------|-----------|------------------|
| 1-2  | Baseline Training | 70-75% |
| 3-4  | Fine-tuning | 80-85% |
| 5-6  | Advanced Optimization | 85-90% |
| 7-8  | Ensemble & Polish | 90-95% |

## 🚨 Troubleshooting

### Common Issues

1. **Out of Memory Error**
   ```yaml
   training:
     batch_size: 4  # Reduce batch size
     accumulate_grad_batches: 4  # Maintain effective batch size
   ```

2. **Slow Convergence**
   ```yaml
   optimizer:
     lr: 1e-3  # Increase learning rate
   scheduler:
     type: "cosine"  # Use cosine annealing
   ```

3. **Overfitting**
   ```yaml
   training:
     early_stopping_patience: 10
   augmentation:
     enabled: true  # Increase augmentation
   ```

### Performance Optimization

1. **Mixed Precision Training**
   ```yaml
   training:
     precision: 16  # Use FP16
   ```

2. **Gradient Accumulation**
   ```yaml
   training:
     accumulate_grad_batches: 2  # Effective batch size = batch_size * 2
   ```

## 📈 Next Steps for 90%+ Accuracy

1. **Ensemble Methods**: Combine multiple models
2. **Test-Time Augmentation**: Multiple predictions per image
3. **Model Distillation**: Transfer knowledge from larger models
4. **Active Learning**: Iteratively improve dataset
5. **Custom Architecture**: Particle-specific modifications

## 🔗 Integration with Existing System

After training, integrate the model:

```python
# Update models/transformer_detector.py
class TransformerParticleDetector:
    def __init__(self, model_path="path/to/trained/model"):
        self.model = torch.load(model_path)
        # ... rest of implementation
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review training logs and metrics
3. Experiment with different configurations
4. Consider ensemble methods for final performance boost

**Target: 90%+ accuracy within 8-10 weeks of focused development!** 🎯
