# Oil Particle Detection System - Executable Distribution

## 🎯 Overview
Professional oil particle analysis and classification system with modern interface.

## 📦 What's Included
- `Oil Particle Detector.exe` - Main application executable
- `install.bat` - System-wide installer (run as administrator)
- `README_EXECUTABLE.md` - This file

## 🚀 Quick Start

### Option 1: Portable Use
1. Double-click `Oil Particle Detector.exe`
2. The application will start immediately
3. No installation required

### Option 2: System Installation
1. Right-click `install.bat` and select "Run as administrator"
2. Follow the installation prompts
3. A desktop shortcut will be created
4. Access from Start Menu or desktop

## 💡 Features
- **Professional Interface** - Modern, intuitive design
- **Particle Detection** - Advanced computer vision algorithms
- **Classification** - Automatic particle type identification
- **Wear Analysis** - Oil condition assessment
- **Export Results** - Save analysis reports
- **High DPI Support** - Crisp display on all monitors

## 🔧 System Requirements
- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space
- Graphics card with OpenGL support

## 📊 Supported Image Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- TIFF (.tiff, .tif)
- BMP (.bmp)

## 🎮 How to Use
1. **Open Image**: Click "📁 Open Image" to load your oil sample image
2. **Analyze**: Click "🔍 Analyze Particles" to start detection
3. **Review Results**: Check the detection results and statistics
4. **Export**: Click "💾 Export Results" to save your analysis

## 📈 Analysis Results
The system provides:
- **Particle Count** - Total number of detected particles
- **Particle Types** - Classification by wear type:
  - Normal Wear
  - Cutting Wear  
  - Metal Debris
  - Metallic Spheres
  - Other Types
- **Wear Stage** - Overall oil condition assessment
- **Statistics** - Detailed numerical analysis

## 🛠️ Troubleshooting

### Application Won't Start
- Ensure you have Windows 10/11
- Try running as administrator
- Check antivirus isn't blocking the file

### Poor Detection Results
- Ensure good image quality and lighting
- Use appropriate magnification
- Clean the sample slide before imaging

### Performance Issues
- Close other applications to free memory
- Ensure adequate disk space
- Update graphics drivers

## 📞 Support
For technical support or questions:
- Check the troubleshooting section above
- Ensure you're using the latest version
- Contact your system administrator

## 📄 License
This software is provided for oil analysis purposes.
All rights reserved.

---
**Oil Particle Detection System v1.0**  
Professional Oil Analysis Solutions
