"""
Oil Particle Detection System - Application Launcher
Professional standalone application entry point
"""

import sys
import os
from pathlib import Path

def main():
    """Main application launcher"""
    print("🔬 Oil Particle Detection System v2.0")
    print("=" * 50)
    print("Starting Professional Application...")

    try:
        # Check if PyQt5 is available
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 GUI framework loaded")

        # Import and run the standalone application
        from oil_particle_detector_app import main as app_main

        print("✅ Application modules loaded")
        print("🚀 Launching GUI application...")

        return app_main()

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n📋 Required dependencies:")
        print("   - PyQt5 (GUI framework)")
        print("   - OpenCV (image processing)")
        print("   - NumPy (numerical computing)")
        print("\n💡 Install dependencies:")
        print("   pip install -r requirements/requirements.txt")
        print("   or")
        print("   pip install PyQt5 opencv-python numpy")
        return 1

    except Exception as e:
        print(f"❌ Application error: {e}")
        print("\n🔧 Troubleshooting:")
        print("   1. Check if all files are in place")
        print("   2. Verify Python version (3.8+)")
        print("   3. Reinstall dependencies")
        return 1

if __name__ == '__main__':
    exit_code = main()
    if exit_code == 0:
        print("\n✅ Application closed successfully")
    else:
        print(f"\n❌ Application exited with code: {exit_code}")
    sys.exit(exit_code)
