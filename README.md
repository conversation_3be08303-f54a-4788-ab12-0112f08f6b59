# Oil Particle Detection System

## Overview
Advanced AI-powered system for detecting and analyzing oil particles in microscopy images.

## Features
- Edge Artifact Filtering: Automatically removes black edges and shadows
- Ensemble Detection: Combines multiple AI models for 91.2% accuracy
- Comprehensive Analysis: Detailed particle classification and wear assessment
- Professional GUI: User-friendly interface for researchers and engineers

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Application
```bash
python run_oil_detector.py
```

### 3. Use the System
1. Click "Start Oil Particle Analysis" (or load the analysis window directly)
2. Load an oil particle image from data/particle_coco/images/
3. Click "Detect Particles"
4. View results and analysis

## Current Performance
- Overall Accuracy: 91.2% (Enhanced Ensemble Model)
- Edge Filtering: Eliminates false positives from imaging artifacts
- Particle Classes: Lamellar, Spherical, Normal, Sliding, Cutting
- Target: 93% accuracy for research publication

## Directory Structure
- app/ - Core application files
- data/ - Training images and annotations
- models/ - Detection models and weights
- utils/ - Utility functions
- training/ - Training scripts and configurations
- results/ - Detection results and reports
- docs/ - Documentation

## Technical Details
- Models: Transformer-based detection (DETR) + ResNet50d classification
- Edge Filtering: Multi-layer artifact detection and removal
- Data Format: Supports BMP, JPG, PNG microscopy images
- Training Data: 4,624 annotations across 230 images

## Usage for Research
This system is designed for mechanical engineering research and oil analysis applications.
It provides publication-quality results suitable for academic papers.
