#!/usr/bin/env python3
"""
Create a portable Oil Particle Detector application
This creates a self-contained folder that can be distributed
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
import subprocess

def create_portable_app():
    """Create a portable application folder"""
    print("Creating Portable Oil Particle Detector")
    print("=" * 50)
    
    # Create output directory
    output_dir = Path("Oil_Particle_Detector_Portable")
    if output_dir.exists():
        print(f"Removing existing {output_dir}...")
        shutil.rmtree(output_dir)
    
    output_dir.mkdir()
    print(f"✅ Created output directory: {output_dir}")
    
    # Copy Python files
    print("Copying application files...")
    
    # Copy main files
    files_to_copy = [
        "launch_app.py",
        "requirements.txt",
        "README_EXECUTABLE.md"
    ]
    
    for file in files_to_copy:
        if Path(file).exists():
            shutil.copy2(file, output_dir)
            print(f"  ✅ Copied {file}")
    
    # Copy oil_detector package
    if Path("oil_detector").exists():
        shutil.copytree("oil_detector", output_dir / "oil_detector")
        print("  ✅ Copied oil_detector package")
    
    # Copy sample data if it exists
    if Path("data").exists():
        shutil.copytree("data", output_dir / "data")
        print("  ✅ Copied sample data")
    
    # Create launcher batch file
    create_launcher_batch(output_dir)
    
    # Create installer batch file
    create_installer_batch(output_dir)
    
    # Create requirements installer
    create_requirements_installer(output_dir)
    
    print(f"\n🎉 Portable application created successfully!")
    print(f"📁 Location: {output_dir.absolute()}")
    print(f"📊 Size: {get_folder_size(output_dir):.1f} MB")
    
    return True

def create_launcher_batch(output_dir):
    """Create a batch file to launch the application"""
    launcher_content = '''@echo off
title Oil Particle Detection System
echo Oil Particle Detection System - Launcher
echo ==========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo [OK] Python found
echo.

REM Check if required packages are installed
echo Checking dependencies...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] PyQt5 not found. Installing dependencies...
    echo.
    call install_requirements.bat
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo [OK] Dependencies found
)

echo.
echo Starting Oil Particle Detection System...
echo.

REM Launch the application
python launch_app.py

if errorlevel 1 (
    echo.
    echo [ERROR] Application failed to start
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo Application closed normally
'''
    
    launcher_path = output_dir / "Oil Particle Detector.bat"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("  ✅ Created launcher: Oil Particle Detector.bat")

def create_installer_batch(output_dir):
    """Create system installer batch file"""
    installer_content = '''@echo off
echo Oil Particle Detection System - System Installer
echo =================================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if errorlevel 1 (
    echo This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

set "INSTALL_DIR=%PROGRAMFILES%\\Oil Particle Detector"
set "DESKTOP_SHORTCUT=%PUBLIC%\\Desktop\\Oil Particle Detector.lnk"

echo Installing to: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy all files
echo Copying application files...
xcopy /E /I /Y "*" "%INSTALL_DIR%\\" >nul
if errorlevel 1 (
    echo [ERROR] Failed to copy files
    pause
    exit /b 1
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Oil Particle Detector.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Oil Particle Detection System'; $Shortcut.Save()"

REM Create start menu entry
set "START_MENU=%PROGRAMDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\Oil Particle Detector.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Oil Particle Detector.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Oil Particle Detection System'; $Shortcut.Save()"

echo.
echo [SUCCESS] Installation completed successfully!
echo.
echo Desktop shortcut: Oil Particle Detector
echo Start menu entry: Oil Particle Detector
echo Installation location: %INSTALL_DIR%
echo.
echo You can now run the application from:
echo - Desktop shortcut
echo - Start menu
echo - Or navigate to the installation folder
echo.
pause
'''
    
    installer_path = output_dir / "install_system.bat"
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("  ✅ Created system installer: install_system.bat")

def create_requirements_installer(output_dir):
    """Create requirements installer batch file"""
    requirements_content = '''@echo off
echo Installing Python Dependencies
echo ==============================
echo.

echo Installing required packages...
echo This may take a few minutes...
echo.

python -m pip install --upgrade pip
if errorlevel 1 (
    echo [ERROR] Failed to upgrade pip
    exit /b 1
)

python -m pip install PyQt5 opencv-python numpy matplotlib scipy Pillow
if errorlevel 1 (
    echo [ERROR] Failed to install packages
    exit /b 1
)

echo.
echo [SUCCESS] All dependencies installed successfully!
echo.
'''
    
    req_installer_path = output_dir / "install_requirements.bat"
    with open(req_installer_path, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print("  ✅ Created requirements installer: install_requirements.bat")

def get_folder_size(folder_path):
    """Get folder size in MB"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)

def create_distribution_zip():
    """Create a ZIP file for easy distribution"""
    output_dir = Path("Oil_Particle_Detector_Portable")
    if not output_dir.exists():
        print("❌ Portable app directory not found. Run create_portable_app() first.")
        return False
    
    zip_path = Path("Oil_Particle_Detector_v1.0.zip")
    
    print(f"Creating distribution ZIP: {zip_path}")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in output_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(output_dir.parent)
                zipf.write(file_path, arcname)
    
    zip_size = zip_path.stat().st_size / (1024 * 1024)
    print(f"✅ Distribution ZIP created: {zip_path} ({zip_size:.1f} MB)")
    
    return True

def main():
    """Main function"""
    print("Oil Particle Detection System - Portable App Creator")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("oil_detector").exists():
        print("❌ Error: oil_detector package not found")
        print("Please run this script from the project root directory")
        return False
    
    # Create portable app
    if not create_portable_app():
        return False
    
    # Create distribution ZIP
    create_distribution_zip()
    
    print("\n" + "=" * 60)
    print("🎉 SUCCESS! Portable application created!")
    print("=" * 60)
    print()
    print("📁 Portable App: Oil_Particle_Detector_Portable/")
    print("📦 Distribution: Oil_Particle_Detector_v1.0.zip")
    print()
    print("To use:")
    print("1. Extract the ZIP file on target computer")
    print("2. Double-click 'Oil Particle Detector.bat'")
    print("3. For system-wide install, run 'install_system.bat' as admin")
    print()
    print("Requirements:")
    print("- Python 3.8+ installed on target system")
    print("- Internet connection for first-time dependency installation")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Failed to create portable app")
        sys.exit(1)
    else:
        print("\n✅ Portable app creation completed successfully!")
