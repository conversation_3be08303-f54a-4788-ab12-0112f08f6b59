# DETR Training Configuration for Oil Particle Detection
# Optimized for achieving 90%+ accuracy

# Model Configuration
model:
  name: "facebook/detr-resnet-50"
  num_classes: 6  # 5 particle types + background (Lamellar, Spherical, Normal, Sliding, Cutting)
  backbone: "resnet50"

# Data Configuration
data:
  train_dir: "data/particle_coco"
  val_dir: "data/particle_coco"
  test_dir: "data/particle_coco"
  train_annotations: "data/particle_coco/annotations_train.json"
  val_annotations: "data/particle_coco/annotations_val.json"
  max_size: 800
  augment: true
  num_workers: 4

# Training Configuration
training:
  max_epochs: 100
  batch_size: 8  # Adjust based on GPU memory
  precision: 16  # Mixed precision for faster training
  gradient_clip_val: 1.0
  accumulate_grad_batches: 2  # Effective batch size = 16
  val_check_interval: 1.0
  early_stopping_patience: 15
  freeze_backbone: false  # Set to true for initial training

# Optimizer Configuration
optimizer:
  type: "adamw"
  lr: 1e-4
  weight_decay: 1e-4
  betas: [0.9, 0.999]

# Scheduler Configuration
scheduler:
  type: "cosine"
  T_max: 100
  eta_min: 1e-6

# Loss Configuration
loss:
  type: "detr_loss"  # Use DETR's built-in loss
  class_loss_coef: 1.0
  bbox_loss_coef: 5.0
  giou_loss_coef: 2.0
  focal_alpha: 0.25
  focal_gamma: 2.0

# Experiment Tracking
wandb:
  enabled: true
  project: "oil-particle-detection"
  run_name: "detr-resnet50-v1"
  tags: ["detr", "transformer", "particle-detection"]

# Data Augmentation
augmentation:
  enabled: true
  horizontal_flip: 0.5
  vertical_flip: 0.2
  rotation: 15
  brightness: 0.2
  contrast: 0.2
  saturation: 0.1
  hue: 0.05
  gaussian_noise: 0.01
  motion_blur: 0.1
  elastic_transform: 0.2

# Evaluation
evaluation:
  metrics: ["mAP", "mAP_50", "mAP_75", "precision", "recall"]
  iou_thresholds: [0.5, 0.75, 0.9]
  confidence_threshold: 0.5
  nms_threshold: 0.5
