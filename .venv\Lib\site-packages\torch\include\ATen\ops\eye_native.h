#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor eye(int64_t n, ::std::optional<at::ScalarType> dtype={}, ::std::optional<at::Layout> layout={}, ::std::optional<at::Device> device={}, ::std::optional<bool> pin_memory={});
TORCH_API at::Tensor & eye_out_cpu(int64_t n, at::Tensor & out);
TORCH_API at::Tensor & eye_out_cuda(int64_t n, at::Tensor & out);
TORCH_API at::Tensor eye(int64_t n, int64_t m, ::std::optional<at::ScalarType> dtype={}, ::std::optional<at::Layout> layout={}, ::std::optional<at::Device> device={}, ::std::optional<bool> pin_memory={});
TORCH_API at::Tensor & eye_out_cpu(int64_t n, int64_t m, at::Tensor & out);
TORCH_API at::Tensor & eye_out_cuda(int64_t n, int64_t m, at::Tensor & out);
} // namespace native
} // namespace at
