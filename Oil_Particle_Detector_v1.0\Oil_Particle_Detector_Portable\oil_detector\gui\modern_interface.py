"""
Modern Professional Interface for Oil Particle Detection
Matches the style shown in the reference image
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ModernParticleInterface(QMainWindow):
    """Modern professional interface matching the reference design"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Oil Particle Analysis - Detection & Classification")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # Apply modern styling
        self.apply_professional_style()
        
        # Create the interface
        self.create_interface()
    
    def apply_professional_style(self):
        """Apply professional styling matching the reference"""
        style = """
        QMainWindow {
            background-color: #f0f0f0;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        QWidget {
            font-size: 9pt;
        }
        
        /* Header styling */
        .header {
            background-color: #2c3e50;
            color: white;
            font-size: 16pt;
            font-weight: bold;
            padding: 10px;
        }
        
        /* Button styling */
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            min-height: 25px;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        /* Special button colors */
        .open-btn {
            background-color: #f39c12;
        }
        .open-btn:hover {
            background-color: #e67e22;
        }
        
        .analyze-btn {
            background-color: #3498db;
        }
        .analyze-btn:hover {
            background-color: #2980b9;
        }
        
        .export-btn {
            background-color: #27ae60;
        }
        .export-btn:hover {
            background-color: #229954;
        }
        
        /* Group box styling */
        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }
        
        /* Tab styling */
        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        
        /* Status and results styling */
        .status-high {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .status-normal {
            color: #27ae60;
            font-weight: bold;
        }
        
        .particle-count {
            font-size: 14pt;
            font-weight: bold;
            color: #2c3e50;
        }
        """
        self.setStyleSheet(style)
    
    def create_interface(self):
        """Create the main interface layout"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Content area
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Image display
        left_panel = self.create_image_panel()
        content_splitter.addWidget(left_panel)
        
        # Right side - Controls and results
        right_panel = self.create_control_panel()
        content_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        content_splitter.setSizes([600, 400])
        
        main_layout.addWidget(content_splitter)
        
        # Status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create the application header"""
        header = QFrame()
        header.setObjectName("header")
        header.setStyleSheet("QFrame#header { background-color: #2c3e50; color: white; }")
        header.setFixedHeight(60)
        
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("Oil Particle Analysis")
        title.setStyleSheet("font-size: 18pt; font-weight: bold; color: white;")
        
        # Back button
        back_btn = QPushButton("← Back to Main")
        back_btn.setStyleSheet("background-color: #7f8c8d; color: white;")
        
        layout.addWidget(title)
        layout.addStretch()
        layout.addWidget(back_btn)
        
        return header
    
    def create_image_panel(self):
        """Create the image display panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Image display area
        self.image_label = QLabel()
        self.image_label.setMinimumSize(400, 300)
        self.image_label.setStyleSheet("border: 2px solid #bdc3c7; background-color: white;")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setText("No image loaded")
        
        layout.addWidget(self.image_label)
        
        return panel
    
    def create_control_panel(self):
        """Create the control and results panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Analysis controls
        controls_group = self.create_analysis_controls()
        layout.addWidget(controls_group)
        
        # Results tabs
        results_tabs = self.create_results_tabs()
        layout.addWidget(results_tabs)
        
        return panel
    
    def create_analysis_controls(self):
        """Create analysis control buttons"""
        group = QGroupBox("Analysis Controls")
        layout = QVBoxLayout(group)
        
        # Main action buttons
        button_layout = QHBoxLayout()
        
        open_btn = QPushButton("📁 Open Image")
        open_btn.setObjectName("open-btn")
        open_btn.setStyleSheet("background-color: #f39c12;")
        
        analyze_btn = QPushButton("🔍 Analyze Particles")
        analyze_btn.setObjectName("analyze-btn")
        analyze_btn.setStyleSheet("background-color: #3498db;")
        
        export_btn = QPushButton("💾 Export Results")
        export_btn.setObjectName("export-btn")
        export_btn.setStyleSheet("background-color: #27ae60;")
        
        button_layout.addWidget(open_btn)
        button_layout.addWidget(analyze_btn)
        button_layout.addWidget(export_btn)
        
        layout.addLayout(button_layout)
        
        return group
    
    def create_results_tabs(self):
        """Create results tabs matching the reference"""
        tabs = QTabWidget()
        
        # Detection Results tab
        detection_tab = self.create_detection_results_tab()
        tabs.addTab(detection_tab, "🔍 Detection Results")
        
        # Statistics tab
        stats_tab = self.create_statistics_tab()
        tabs.addTab(stats_tab, "📊 Statistics")
        
        # Wear Analysis tab
        wear_tab = self.create_wear_analysis_tab()
        tabs.addTab(wear_tab, "⚙️ Wear Analysis")
        
        return tabs
    
    def create_detection_results_tab(self):
        """Create detection results tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Wear stage classification
        wear_group = QGroupBox("Wear Stage Classification")
        wear_layout = QVBoxLayout(wear_group)
        
        wear_status = QLabel("Wear Stage: Not analyzed")
        wear_status.setStyleSheet("font-size: 12pt; color: #7f8c8d;")
        wear_layout.addWidget(wear_status)
        
        layout.addWidget(wear_group)
        
        # Detected particle types
        particles_group = QGroupBox("Detected Particle Types")
        particles_layout = QVBoxLayout(particles_group)
        
        # Sample particle type entries
        particle_types = [
            ("Normal Wear", "88 (50.6%)", "#27ae60"),
            ("Cutting Wear", "0 (0.0%)", "#e74c3c"),
            ("Metal Debris", "40 (23.5%)", "#f39c12"),
            ("Metallic Spheres", "26 (16.5%)", "#3498db"),
            ("Other Types", "16 (9.4%)", "#95a5a6")
        ]
        
        for ptype, count, color in particle_types:
            type_layout = QHBoxLayout()
            
            color_indicator = QLabel("●")
            color_indicator.setStyleSheet(f"color: {color}; font-size: 14pt;")
            
            type_label = QLabel(f"{ptype}: {count}")
            type_label.setStyleSheet("font-size: 10pt;")
            
            type_layout.addWidget(color_indicator)
            type_layout.addWidget(type_label)
            type_layout.addStretch()
            
            particles_layout.addLayout(type_layout)
        
        layout.addWidget(particles_group)
        
        # Analysis summary
        summary_group = QGroupBox("Analysis Summary")
        summary_layout = QVBoxLayout(summary_group)
        
        summary_text = QLabel("Analysis completed! Found 170 particles.")
        summary_text.setStyleSheet("font-size: 11pt; color: #2c3e50;")
        summary_layout.addWidget(summary_text)
        
        layout.addWidget(summary_group)
        
        return widget
    
    def create_statistics_tab(self):
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        stats_label = QLabel("Detailed statistics will be displayed here")
        stats_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(stats_label)
        
        return widget
    
    def create_wear_analysis_tab(self):
        """Create wear analysis tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        wear_label = QLabel("Wear analysis results will be displayed here")
        wear_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(wear_label)
        
        return widget
    
    def create_status_bar(self):
        """Create status bar"""
        status_bar = self.statusBar()
        
        # Particle count status
        particle_status = QLabel("Detected Particles: 170 found (High particle count)")
        particle_status.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        status_bar.addWidget(particle_status)
        status_bar.addPermanentWidget(QLabel("4:45 PM - 7/3/2025"))

def main():
    """Test the modern interface"""
    app = QApplication([])
    window = ModernParticleInterface()
    window.show()
    return app.exec_()

if __name__ == "__main__":
    main()
