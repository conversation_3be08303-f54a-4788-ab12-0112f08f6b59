# Oil Particle Detection System - Directory Guide

## 🚀 Quick Start
- **Run System**: `python run_oil_detector.py` or double-click `run_system.bat`
- **Load Images**: From `data/particle_coco/images/`
- **View Results**: In `results/` directory

## 📁 Directory Structure
- `app/` - Core application files
- `data/` - Training images and annotations  
- `training/` - Model training scripts
- `results/` - Detection results and reports
- `docs/` - Documentation and guides
- `tests/` - Testing and validation
- `requirements/` - Dependencies and installation
- `archive/` - Old files and backups

## 📊 Current Status
- **System**: Ready to run
- **Accuracy**: 91.2% (Enhanced Ensemble)
- **Edge Filtering**: Enabled
- **Training Data**: 4,624 annotations ready
