import os
import json
import shutil
from glob import glob
from PIL import Image

# Paths
YOLO_IMAGES_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../02_DATA/custom/images'))
YOLO_LABELS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../02_DATA/custom/labels'))
CLASSES_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../02_DATA/custom/classes.names'))
COCO_OUTPUT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../02_DATA/coco_export'))
COCO_IMAGES_DIR = os.path.join(COCO_OUTPUT_DIR, 'images')
COCO_ANNOTATIONS_PATH = os.path.join(COCO_OUTPUT_DIR, 'annotations.json')

os.makedirs(COCO_OUTPUT_DIR, exist_ok=True)
os.makedirs(COCO_IMAGES_DIR, exist_ok=True)

# Read class names
def read_classes(classes_path):
    with open(classes_path, 'r', encoding='utf-8') as f:
        classes = [line.strip() for line in f if line.strip()]
    return classes

# Convert YOLO bbox to COCO bbox
def yolo_to_coco_bbox(yolo_bbox, img_w, img_h):
    x_center, y_center, w, h = yolo_bbox
    x = (x_center - w / 2) * img_w
    y = (y_center - h / 2) * img_h
    width = w * img_w
    height = h * img_h
    return [x, y, width, height]

# Main conversion
def convert_yolo_to_coco():
    classes = read_classes(CLASSES_PATH)
    images = []
    annotations = []
    image_id = 1
    annotation_id = 1

    label_files = sorted(glob(os.path.join(YOLO_LABELS_DIR, '*.txt')))
    for label_path in label_files:
        base = os.path.splitext(os.path.basename(label_path))[0]
        img_path = os.path.join(YOLO_IMAGES_DIR, f'{base}.bmp')
        if not os.path.exists(img_path):
            print(f'Warning: Image not found for label {label_path}')
            continue
        # Copy image to COCO images dir
        new_img_path = os.path.join(COCO_IMAGES_DIR, f'{base}.bmp')
        shutil.copy(img_path, new_img_path)
        # Get image size
        with Image.open(img_path) as img:
            width, height = img.size
        # Add image entry
        images.append({
            'id': image_id,
            'file_name': f'{base}.bmp',
            'width': width,
            'height': height
        })
        # Read label file
        with open(label_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) != 5:
                    continue
                class_id = int(parts[0])
                bbox = list(map(float, parts[1:]))
                coco_bbox = yolo_to_coco_bbox(bbox, width, height)
                annotations.append({
                    'id': annotation_id,
                    'image_id': image_id,
                    'category_id': class_id + 1,  # COCO category ids start at 1
                    'bbox': coco_bbox,
                    'area': coco_bbox[2] * coco_bbox[3],
                    'iscrowd': 0,
                    'segmentation': []
                })
                annotation_id += 1
        image_id += 1

    # COCO categories
    categories = [
        {'id': i + 1, 'name': name, 'supercategory': 'particle'}
        for i, name in enumerate(classes)
    ]

    coco_dict = {
        'images': images,
        'annotations': annotations,
        'categories': categories
    }

    with open(COCO_ANNOTATIONS_PATH, 'w', encoding='utf-8') as f:
        json.dump(coco_dict, f, indent=2, ensure_ascii=False)
    print(f'COCO dataset created at {COCO_OUTPUT_DIR}')

if __name__ == '__main__':
    convert_yolo_to_coco() 