# Oil Particle Detection System - RT-DETR Enhanced

A cutting-edge, English-language oil particle detection and wear analysis system built with Python 3.12, RT-DETR, and PyQt5.

## Features

- **Modern Python 3.12 Compatibility**: Fully compatible with the latest Python version
- **English User Interface**: Complete English translation with modern UI design
- **RT-DETR Detection**: State-of-the-art Real-Time Detection Transformer for superior particle detection
- **CNN Wear Classification**: ResNet50d model for wear stage analysis (Early/Middle/Late)
- **Advanced Analysis**: Detailed morphological, texture, and statistical analysis
- **Real-time Results**: Interactive results display with export functionality
- **Professional UI**: Modern PyQt5 interface with responsive design

## System Requirements

- **Python**: 3.8 or higher (tested with Python 3.12)
- **Operating System**: Windows 10/11 (primary), Linux/macOS (experimental)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space for models and dependencies

## Installation

### 1. Clone or Download
```bash
# If using git
git clone <repository-url>
cd oil_particle_detector_py312

# Or download and extract the folder
```

### 2. Install Dependencies
```bash
# Install all required packages
pip install -r requirements.txt

# Or install manually
pip install PyQt5 opencv-python torch torchvision scikit-image matplotlib pandas numpy pillow
```

### 3. Verify Installation
```bash
python main.py
```

## Usage

### Starting the Application
```bash
python main.py
```

### Basic Workflow
1. **Launch Application**: Run `python main.py`
2. **Main Window**: Click "Start Oil Particle Analysis"
3. **Load Image**: Click "📁 Open Image" and select an oil particle image
4. **Analyze**: Click "🔍 Analyze Particles" to start detection
5. **Review Results**: Check the Detection Results, Statistics, and Wear Analysis tabs
6. **Export**: Click "💾 Export Results" to save analysis data

### Sample Images
The system includes sample images in `data/samples/`:
- `beijing.jpg` - Sample oil particle image
- `beijing1.jpg` - Additional sample
- `beijing2.jpg` - Additional sample

## File Structure

```
oil_particle_detector_py312/
├── main.py                 # Main application entry point
├── main_window.py          # Main window UI
├── analysis_window.py      # Analysis window UI
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/                # Configuration files
│   ├── yolov3.cfg         # Legacy YOLOv3 configuration (for compatibility)
│   ├── yolov3-custom.cfg  # Custom configuration
│   └── coco.names         # Object class names
├── weights/               # Model weights
│   ├── yolov3.weights     # Legacy YOLOv3 weights (for compatibility)
│   └── resnet50d_5epochs_accuracy0.80357_weights.pth  # CNN wear classification model
├── models/                # Model definitions
│   └── models.py          # PyTorch model architectures
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── utils.py           # General utilities
│   ├── datasets.py        # Dataset handling
│   ├── parse_config.py    # Configuration parsing
│   └── augmentations.py   # Data augmentation
├── data/                  # Data directory
│   └── samples/           # Sample images
└── output/                # Analysis results output
```

## Technical Details

### Detection Pipeline
1. **Image Loading**: OpenCV-based image processing
2. **RT-DETR Detection**: Real-Time Detection Transformer for particle identification
3. **Morphological Analysis**: Advanced particle shape, size, and texture analysis
4. **CNN Classification**: ResNet50d model for wear stage classification
5. **Statistical Processing**: Comprehensive measurement calculations and distribution analysis

### Particle Types Detected
- **Normal Particles**: Standard wear particles
- **Cutting Particles**: Indicates abrasive wear
- **Sliding Particles**: Indicates adhesive wear  
- **Spherical Particles**: Indicates rolling fatigue
- **Lamellar Particles**: Indicates severe sliding wear

### Wear Stages
- **Early Wear**: Normal operation, minimal particle generation
- **Middle Wear**: Increased particle generation, monitoring recommended
- **Late Wear**: Significant wear, maintenance required

## Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Install missing packages
pip install <missing-package-name>
```

**2. Model Files Missing**
- Ensure all files in `weights/` and `config/` directories are present
- Copy from original kongjian directory if needed

**3. PyQt5 Issues**
```bash
# Reinstall PyQt5
pip uninstall PyQt5
pip install PyQt5
```

**4. CUDA/GPU Issues**
```bash
# Install CPU-only PyTorch if GPU issues occur
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### Performance Tips
- Use GPU acceleration if available (CUDA-compatible GPU)
- Close other applications to free memory during analysis
- Use smaller images for faster processing

## Development

### Adding New Features
1. Modify UI components in `main_window.py` or `analysis_window.py`
2. Add detection algorithms in `models/` directory
3. Update utility functions in `utils/` directory

### Testing
```bash
# Test individual components
python main_window.py      # Test main window
python analysis_window.py  # Test analysis window
```

## Differences from Original System

### Improvements
- ✅ **Python 3.12 Compatibility**: No version conflicts
- ✅ **RT-DETR Technology**: State-of-the-art transformer-based detection
- ✅ **English Interface**: Complete translation from Chinese
- ✅ **Modern UI Design**: Professional, responsive interface
- ✅ **Superior Detection**: Better accuracy for small particles
- ✅ **Advanced Analysis**: Morphological and texture feature extraction
- ✅ **Better Error Handling**: Comprehensive error messages
- ✅ **Modular Architecture**: Clean, maintainable code structure
- ✅ **Enhanced Documentation**: Detailed usage instructions

### Migration from Original
- All functionality preserved from original kongjian.exe
- UI completely translated to English
- Modern dependency management
- Improved file organization
- Better cross-platform compatibility

## License

This project is based on the original oil particle detection system with enhancements for Python 3.12 compatibility and English language support.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all dependencies are installed correctly
3. Ensure model files are present in correct directories
4. Test with provided sample images first
