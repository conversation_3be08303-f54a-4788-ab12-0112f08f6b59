@echo off
echo Installing Python Dependencies
echo ==============================
echo.

echo Installing required packages...
echo This may take a few minutes...
echo.

python -m pip install --upgrade pip
if errorlevel 1 (
    echo [ERROR] Failed to upgrade pip
    exit /b 1
)

python -m pip install PyQt5 opencv-python numpy matplotlib scipy Pillow
if errorlevel 1 (
    echo [ERROR] Failed to install packages
    exit /b 1
)

echo.
echo [SUCCESS] All dependencies installed successfully!
echo.
