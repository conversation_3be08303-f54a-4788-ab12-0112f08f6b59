"""
Training Data Overview for Oil Particle Detection
Comprehensive analysis of your labeled training datasets
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from collections import defaultdict, Counter

def analyze_yolo_dataset():
    """Analyze the YOLO format dataset"""
    
    print("📊 YOLO FORMAT DATASET ANALYSIS")
    print("="*50)
    
    # Check classes
    classes_file = Path("data/custom/classes.names")
    if classes_file.exists():
        with open(classes_file, 'r') as f:
            classes = [line.strip() for line in f.readlines() if line.strip()]
        
        print(f"🏷️ Classes ({len(classes)}):")
        for i, class_name in enumerate(classes):
            print(f"   {i}: {class_name}")
    
    # Count images and labels
    images_dir = Path("data/custom/images")
    labels_dir = Path("data/custom/labels")
    
    image_files = list(images_dir.glob("*.bmp")) if images_dir.exists() else []
    label_files = list(labels_dir.glob("*.txt")) if labels_dir.exists() else []
    
    print(f"\n📁 Dataset Size:")
    print(f"   Images: {len(image_files)}")
    print(f"   Label files: {len(label_files)}")
    
    # Analyze annotations
    if labels_dir.exists():
        class_counts = defaultdict(int)
        total_annotations = 0
        images_with_annotations = 0
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            if lines:
                images_with_annotations += 1
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        class_counts[class_id] += 1
                        total_annotations += 1
        
        print(f"\n📈 Annotation Statistics:")
        print(f"   Total annotations: {total_annotations:,}")
        print(f"   Images with annotations: {images_with_annotations}")
        print(f"   Average annotations per image: {total_annotations/max(images_with_annotations,1):.1f}")
        
        print(f"\n🏷️ Class Distribution:")
        for class_id, count in sorted(class_counts.items()):
            class_name = classes[class_id] if class_id < len(classes) else f"Class_{class_id}"
            percentage = (count / total_annotations) * 100 if total_annotations > 0 else 0
            print(f"   {class_name}: {count:,} ({percentage:.1f}%)")
    
    return {
        'format': 'YOLO',
        'images': len(image_files),
        'labels': len(label_files),
        'classes': classes if 'classes' in locals() else [],
        'total_annotations': total_annotations if 'total_annotations' in locals() else 0
    }

def analyze_coco_dataset():
    """Analyze the COCO format dataset"""
    
    print("\n📊 COCO FORMAT DATASET ANALYSIS")
    print("="*50)
    
    # Analyze training set
    train_file = Path("data/particle_coco/annotations_train.json")
    val_file = Path("data/particle_coco/annotations_val.json")
    full_file = Path("data/particle_coco/annotations_full.json")
    
    datasets = {}
    
    for name, file_path in [("Training", train_file), ("Validation", val_file), ("Full", full_file)]:
        if file_path.exists():
            print(f"\n🔍 {name} Dataset:")
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            images = data.get('images', [])
            annotations = data.get('annotations', [])
            categories = data.get('categories', [])
            
            print(f"   Images: {len(images):,}")
            print(f"   Annotations: {len(annotations):,}")
            print(f"   Categories: {len(categories)}")
            
            # Class distribution
            if annotations:
                class_counts = Counter(ann['category_id'] for ann in annotations)
                print(f"   Class distribution:")
                
                # Create category mapping
                cat_map = {cat['id']: cat['name'] for cat in categories}
                
                for cat_id, count in sorted(class_counts.items()):
                    cat_name = cat_map.get(cat_id, f"Category_{cat_id}")
                    percentage = (count / len(annotations)) * 100
                    print(f"     {cat_name}: {count:,} ({percentage:.1f}%)")
            
            datasets[name.lower()] = {
                'images': len(images),
                'annotations': len(annotations),
                'categories': categories,
                'class_counts': class_counts if 'class_counts' in locals() else {}
            }
    
    return datasets

def check_image_quality():
    """Check image quality and format"""
    
    print("\n📸 IMAGE QUALITY ANALYSIS")
    print("="*50)
    
    # Check both datasets
    image_dirs = [
        ("YOLO Dataset", Path("data/custom/images")),
        ("COCO Dataset", Path("data/particle_coco/images"))
    ]
    
    for dataset_name, img_dir in image_dirs:
        if img_dir.exists():
            print(f"\n🔍 {dataset_name}:")
            
            image_files = list(img_dir.glob("*.bmp"))
            print(f"   Total images: {len(image_files)}")
            
            if image_files:
                # Check a sample image
                sample_img = image_files[0]
                try:
                    import cv2
                    img = cv2.imread(str(sample_img))
                    if img is not None:
                        height, width = img.shape[:2]
                        print(f"   Image dimensions: {width}x{height}")
                        print(f"   Image format: BMP")
                        print(f"   Color channels: {img.shape[2] if len(img.shape) > 2 else 1}")
                        
                        # Check file sizes
                        file_sizes = [f.stat().st_size for f in image_files[:10]]
                        avg_size = np.mean(file_sizes) / (1024 * 1024)  # MB
                        print(f"   Average file size: {avg_size:.1f} MB")
                    
                except ImportError:
                    print("   OpenCV not available for detailed analysis")
                except Exception as e:
                    print(f"   Error analyzing images: {e}")

def create_training_data_summary():
    """Create comprehensive training data summary"""
    
    print("\n📋 TRAINING DATA SUMMARY")
    print("="*60)
    
    # Analyze both datasets
    yolo_data = analyze_yolo_dataset()
    coco_data = analyze_coco_dataset()
    
    # Check image quality
    check_image_quality()
    
    # Overall summary
    print(f"\n🎯 OVERALL TRAINING DATA STATUS")
    print("="*50)
    
    print(f"📁 Available Datasets:")
    print(f"   1. YOLO Format Dataset:")
    print(f"      • Location: data/custom/")
    print(f"      • Images: {yolo_data['images']:,}")
    print(f"      • Annotations: {yolo_data['total_annotations']:,}")
    print(f"      • Classes: {len(yolo_data['classes'])}")
    
    if 'training' in coco_data:
        print(f"   2. COCO Format Dataset:")
        print(f"      • Location: data/particle_coco/")
        print(f"      • Training images: {coco_data['training']['images']:,}")
        print(f"      • Training annotations: {coco_data['training']['annotations']:,}")
        if 'validation' in coco_data:
            print(f"      • Validation images: {coco_data['validation']['images']:,}")
            print(f"      • Validation annotations: {coco_data['validation']['annotations']:,}")
    
    # Data quality assessment
    print(f"\n✅ DATA QUALITY ASSESSMENT:")
    
    total_images = yolo_data['images']
    total_annotations = yolo_data['total_annotations']
    
    if total_images > 0 and total_annotations > 0:
        print(f"   ✅ Dataset size: {total_images} images, {total_annotations:,} annotations")
        print(f"   ✅ Average annotations per image: {total_annotations/total_images:.1f}")
        
        if total_images >= 100:
            print(f"   ✅ Sufficient images for training")
        else:
            print(f"   ⚠️ Limited images - consider data augmentation")
        
        if total_annotations >= 1000:
            print(f"   ✅ Good annotation density")
        else:
            print(f"   ⚠️ Limited annotations - may need more labeling")
        
        print(f"   ✅ Multiple particle classes available")
        print(f"   ✅ Professional BMP format images")
        print(f"   ✅ Both YOLO and COCO formats available")
    
    # Training recommendations
    print(f"\n🎯 TRAINING RECOMMENDATIONS:")
    print(f"   1. Use COCO format for transformer models (DETR)")
    print(f"   2. Use YOLO format for YOLO-based models")
    print(f"   3. Apply data augmentation to increase dataset size")
    print(f"   4. Focus on sliding particle class (likely underrepresented)")
    print(f"   5. Use edge filtering to improve data quality")
    
    return {
        'yolo_dataset': yolo_data,
        'coco_dataset': coco_data,
        'total_images': total_images,
        'total_annotations': total_annotations
    }

def visualize_class_distribution():
    """Create visualization of class distribution"""
    
    print(f"\n📊 Creating class distribution visualization...")
    
    try:
        # Load YOLO classes and counts
        classes_file = Path("data/custom/classes.names")
        if not classes_file.exists():
            print("⚠️ Classes file not found")
            return
        
        with open(classes_file, 'r') as f:
            classes = [line.strip() for line in f.readlines() if line.strip()]
        
        # Count annotations
        labels_dir = Path("data/custom/labels")
        class_counts = defaultdict(int)
        
        if labels_dir.exists():
            for label_file in labels_dir.glob("*.txt"):
                with open(label_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            parts = line.split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                class_counts[class_id] += 1
        
        # Create visualization
        if class_counts:
            class_names = [classes[i] if i < len(classes) else f"Class_{i}" for i in sorted(class_counts.keys())]
            counts = [class_counts[i] for i in sorted(class_counts.keys())]
            
            plt.figure(figsize=(12, 8))
            
            # Bar plot
            bars = plt.bar(class_names, counts, color=['skyblue', 'lightcoral', 'lightgreen', 'gold', 'plum'])
            plt.title('Oil Particle Class Distribution in Training Data', fontsize=16, fontweight='bold')
            plt.xlabel('Particle Class', fontsize=12)
            plt.ylabel('Number of Annotations', fontsize=12)
            plt.xticks(rotation=45)
            
            # Add count labels on bars
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + max(counts)*0.01,
                        f'{count:,}', ha='center', va='bottom', fontweight='bold')
            
            plt.tight_layout()
            plt.savefig('training_data_class_distribution.png', dpi=300, bbox_inches='tight')
            print("✅ Class distribution saved: training_data_class_distribution.png")
            plt.show()
        
    except Exception as e:
        print(f"❌ Error creating visualization: {e}")

def main():
    """Main function"""
    
    print("🔍 OIL PARTICLE DETECTION - TRAINING DATA ANALYSIS")
    print("="*60)
    print("Analyzing your labeled training datasets...")
    
    # Create comprehensive analysis
    summary = create_training_data_summary()
    
    # Create visualization
    visualize_class_distribution()
    
    print(f"\n🎉 TRAINING DATA ANALYSIS COMPLETE!")
    print(f"📁 Your labeled training data is located in:")
    print(f"   • data/custom/ (YOLO format)")
    print(f"   • data/particle_coco/ (COCO format)")
    print(f"📊 Generated: training_data_class_distribution.png")
    
    return summary

if __name__ == '__main__':
    main()
