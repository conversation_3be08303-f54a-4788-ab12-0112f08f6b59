"""
Diagnostic Script for Oil Particle Detection System
Checks for common issues preventing the system from opening
"""

import sys
import os
import importlib
import traceback

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 PYTHON VERSION CHECK")
    print("="*40)
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is compatible")
        return True
    else:
        print("❌ Python version too old. Need Python 3.8+")
        return False

def check_required_packages():
    """Check if required packages are installed"""
    print("\n📦 PACKAGE DEPENDENCY CHECK")
    print("="*40)
    
    required_packages = [
        ('PyQt5', 'PyQt5.QtWidgets'),
        ('OpenCV', 'cv2'),
        ('NumPy', 'numpy'),
        ('Pillow', 'PIL'),
        ('Matplotlib', 'matplotlib'),
        ('Torch', 'torch'),
        ('Transformers', 'transformers'),
        ('Scikit-Image', 'skimage'),
        ('Pandas', 'pandas')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name}: Available")
        except ImportError as e:
            print(f"❌ {package_name}: Missing - {e}")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All required packages are available")
        return True

def check_pyqt5_display():
    """Check if PyQt5 can create a display"""
    print("\n🖥️ DISPLAY SYSTEM CHECK")
    print("="*40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt
        
        # Try to create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ QApplication created successfully")
        
        # Try to create a simple widget
        widget = QWidget()
        widget.setWindowTitle("Test Window")
        widget.resize(200, 100)
        
        print("✅ QWidget created successfully")
        print("✅ Display system is working")
        
        # Clean up
        widget.close()
        return True
        
    except Exception as e:
        print(f"❌ Display system error: {e}")
        print("💡 This might be a display/graphics driver issue")
        return False

def check_main_files():
    """Check if main application files exist"""
    print("\n📁 APPLICATION FILES CHECK")
    print("="*40)
    
    required_files = [
        'main.py',
        'main_window.py',
        'analysis_window.py',
        'models/transformer_detector.py',
        'models/particle_analyzer.py',
        'utils/edge_artifact_filter.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Found")
        else:
            print(f"❌ {file_path}: Missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ All application files are present")
        return True

def test_import_main_modules():
    """Test importing main application modules"""
    print("\n🔧 MODULE IMPORT TEST")
    print("="*40)
    
    modules_to_test = [
        ('main_window', 'MainWindow'),
        ('analysis_window', 'AnalysisWindow'),
        ('models.transformer_detector', 'TransformerParticleDetector'),
        ('models.particle_analyzer', 'ParticleAnalyzer')
    ]
    
    import_errors = []
    
    for module_name, class_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name}: Import successful")
            else:
                print(f"❌ {module_name}.{class_name}: Class not found")
                import_errors.append(f"{module_name}.{class_name}")
        except Exception as e:
            print(f"❌ {module_name}: Import failed - {e}")
            import_errors.append(module_name)
    
    if import_errors:
        print(f"\n⚠️ Import errors: {', '.join(import_errors)}")
        return False
    else:
        print("\n✅ All modules import successfully")
        return True

def test_simple_gui():
    """Test creating a simple GUI window"""
    print("\n🪟 SIMPLE GUI TEST")
    print("="*40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import QTimer
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create simple test window
        window = QMainWindow()
        window.setWindowTitle("Oil Particle Detector - System Test")
        window.setGeometry(100, 100, 400, 200)
        
        label = QLabel("System Test - If you see this, GUI is working!", window)
        label.setGeometry(50, 80, 300, 40)
        
        window.show()
        print("✅ Test window created and shown")
        
        # Auto-close after 3 seconds
        timer = QTimer()
        timer.timeout.connect(window.close)
        timer.start(3000)
        
        print("💡 Test window will close automatically in 3 seconds")
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        traceback.print_exc()
        return False

def provide_solutions(issues):
    """Provide solutions for identified issues"""
    print("\n🔧 RECOMMENDED SOLUTIONS")
    print("="*40)
    
    if 'python_version' in issues:
        print("1. 🐍 Update Python:")
        print("   • Download Python 3.8+ from python.org")
        print("   • Or use conda: conda install python=3.11")
    
    if 'packages' in issues:
        print("2. 📦 Install missing packages:")
        print("   • pip install -r requirements.txt")
        print("   • Or: pip install PyQt5 opencv-python numpy torch transformers")
    
    if 'display' in issues:
        print("3. 🖥️ Fix display issues:")
        print("   • Update graphics drivers")
        print("   • Try: set QT_QPA_PLATFORM=windows")
        print("   • Or run with: python -c \"import os; os.environ['QT_QPA_PLATFORM']='windows'; exec(open('main.py').read())\"")
    
    if 'files' in issues:
        print("4. 📁 Restore missing files:")
        print("   • Re-download the project")
        print("   • Check if files were moved or deleted")
    
    if 'imports' in issues:
        print("5. 🔧 Fix import issues:")
        print("   • Check PYTHONPATH")
        print("   • Reinstall packages: pip install --force-reinstall PyQt5")
    
    print("\n💡 Quick Fix Commands:")
    print("pip install --upgrade PyQt5 opencv-python numpy")
    print("python -c \"from PyQt5.QtWidgets import QApplication; print('PyQt5 OK')\"")

def main():
    """Main diagnostic function"""
    print("🔍 OIL PARTICLE DETECTION SYSTEM - DIAGNOSTIC")
    print("="*60)
    print("Checking why the system is not opening...")
    
    issues = []
    
    # Run all checks
    if not check_python_version():
        issues.append('python_version')
    
    if not check_required_packages():
        issues.append('packages')
    
    if not check_pyqt5_display():
        issues.append('display')
    
    if not check_main_files():
        issues.append('files')
    
    if not test_import_main_modules():
        issues.append('imports')
    
    # Summary
    print("\n📋 DIAGNOSTIC SUMMARY")
    print("="*40)
    
    if not issues:
        print("✅ No issues found! System should work.")
        print("💡 Try running: python main.py")
        
        # Test simple GUI
        print("\n🧪 Running GUI test...")
        if test_simple_gui():
            print("✅ GUI test passed! System is ready.")
        else:
            print("❌ GUI test failed. Check display settings.")
            issues.append('gui_test')
    else:
        print(f"❌ Found {len(issues)} issue(s):")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    # Provide solutions
    if issues:
        provide_solutions(issues)
    
    print(f"\n🎯 NEXT STEPS:")
    if not issues:
        print("   1. Run: python main.py")
        print("   2. Load an oil particle image")
        print("   3. Click 'Detect Particles'")
    else:
        print("   1. Fix the issues listed above")
        print("   2. Run this diagnostic again")
        print("   3. Then try: python main.py")
    
    return len(issues) == 0

if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎉 System is ready to run!")
    else:
        print("\n⚠️ Please fix the issues before running the system.")
