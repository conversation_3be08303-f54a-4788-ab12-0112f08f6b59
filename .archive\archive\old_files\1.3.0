Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: albumentations in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (2.0.8)
Requirement already satisfied: numpy>=1.24.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (1.26.4)
Requirement already satisfied: scipy>=1.10.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (1.16.0)
Requirement already satisfied: PyYAML in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (6.0.2)
Requirement already satisfied: pydantic>=2.9.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (2.11.7)
Requirement already satisfied: albucore==0.0.24 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (0.0.24)
Requirement already satisfied: opencv-python-headless>=******** in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albumentations) (4.11.0.86)
Requirement already satisfied: stringzilla>=3.10.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albucore==0.0.24->albumentations) (3.12.5)
Requirement already satisfied: simsimd>=5.9.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from albucore==0.0.24->albumentations) (6.4.9)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from pydantic>=2.9.2->albumentations) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from pydantic>=2.9.2->albumentations) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from pydantic>=2.9.2->albumentations) (4.14.0)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from pydantic>=2.9.2->albumentations) (0.4.1)
