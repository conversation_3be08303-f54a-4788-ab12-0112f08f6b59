"""
Practical Oil Particle Detector
Uses computer vision techniques to actually detect particles in images
"""

import cv2
import numpy as np
from skimage import measure, morphology, filters, segmentation
from skimage.feature import graycomatrix, graycoprops
import matplotlib.pyplot as plt
from scipy import ndimage
import math

class PracticalParticleDetector:
    def __init__(self):
        self.min_particle_area = 10  # Minimum particle area in pixels
        self.max_particle_area = 5000  # Maximum particle area in pixels
        
    def detect_particles(self, image_path):
        """
        Detect particles in an image using computer vision techniques
        Returns detailed particle information
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
                
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Preprocessing
            processed = self._preprocess_image(gray)
            
            # Detect particles
            particles = self._detect_particles_cv(processed, gray)
            
            # Analyze each particle
            analyzed_particles = []
            for i, particle in enumerate(particles):
                analysis = self._analyze_particle(particle, gray, i+1)
                if analysis:
                    analyzed_particles.append(analysis)
            
            # Create visualization
            visualization = self._create_visualization(image, analyzed_particles)
            
            return {
                'particles': analyzed_particles,
                'total_count': len(analyzed_particles),
                'image_shape': image.shape,
                'visualization': visualization,
                'summary': self._create_summary(analyzed_particles)
            }
            
        except Exception as e:
            print(f"Error in particle detection: {e}")
            return {
                'particles': [],
                'total_count': 0,
                'image_shape': (0, 0, 0),
                'visualization': None,
                'summary': {},
                'error': str(e)
            }
    
    def _preprocess_image(self, gray):
        """Preprocess image for better particle detection"""
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)
        
        # Apply median filter to remove salt and pepper noise
        filtered = cv2.medianBlur(enhanced, 3)
        
        return filtered
    
    def _detect_particles_cv(self, processed_image, original_gray):
        """Detect particles using computer vision techniques"""
        particles = []
        
        # Method 1: Threshold-based detection
        particles.extend(self._threshold_detection(processed_image))
        
        # Method 2: Edge-based detection
        particles.extend(self._edge_detection(processed_image))
        
        # Method 3: Blob detection
        particles.extend(self._blob_detection(processed_image))
        
        # Remove duplicates and filter by size
        particles = self._filter_and_deduplicate(particles)
        
        return particles
    
    def _threshold_detection(self, image):
        """Detect particles using adaptive thresholding"""
        particles = []
        
        # Adaptive threshold
        thresh = cv2.adaptiveThreshold(image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY_INV, 11, 2)
        
        # Morphological operations to clean up
        kernel = np.ones((2,2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_particle_area <= area <= self.max_particle_area:
                particles.append({
                    'contour': contour,
                    'area': area,
                    'method': 'threshold'
                })
        
        return particles
    
    def _edge_detection(self, image):
        """Detect particles using edge detection"""
        particles = []
        
        # Canny edge detection
        edges = cv2.Canny(image, 50, 150)
        
        # Dilate edges to close gaps
        kernel = np.ones((2,2), np.uint8)
        dilated = cv2.dilate(edges, kernel, iterations=1)
        
        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_particle_area <= area <= self.max_particle_area:
                particles.append({
                    'contour': contour,
                    'area': area,
                    'method': 'edge'
                })
        
        return particles
    
    def _blob_detection(self, image):
        """Detect particles using blob detection"""
        particles = []
        
        # Setup SimpleBlobDetector parameters
        params = cv2.SimpleBlobDetector_Params()
        
        # Filter by Area
        params.filterByArea = True
        params.minArea = self.min_particle_area
        params.maxArea = self.max_particle_area
        
        # Filter by Circularity
        params.filterByCircularity = False
        
        # Filter by Convexity
        params.filterByConvexity = False
        
        # Filter by Inertia
        params.filterByInertia = False
        
        # Create detector
        detector = cv2.SimpleBlobDetector_create(params)
        
        # Detect blobs
        keypoints = detector.detect(image)
        
        for kp in keypoints:
            # Create approximate contour for blob
            center = (int(kp.pt[0]), int(kp.pt[1]))
            radius = int(kp.size / 2)
            
            # Create circular contour
            angles = np.linspace(0, 2*np.pi, 20)
            contour_points = []
            for angle in angles:
                x = int(center[0] + radius * np.cos(angle))
                y = int(center[1] + radius * np.sin(angle))
                contour_points.append([x, y])
            
            contour = np.array(contour_points, dtype=np.int32).reshape(-1, 1, 2)
            area = np.pi * radius * radius
            
            particles.append({
                'contour': contour,
                'area': area,
                'method': 'blob',
                'center': center,
                'radius': radius
            })
        
        return particles
    
    def _filter_and_deduplicate(self, particles):
        """Remove duplicate particles and filter by size"""
        if not particles:
            return []
        
        # Sort by area (largest first)
        particles.sort(key=lambda x: x['area'], reverse=True)
        
        # Remove duplicates based on proximity
        filtered = []
        for particle in particles:
            # Get center of current particle
            if 'center' in particle:
                center1 = particle['center']
            else:
                M = cv2.moments(particle['contour'])
                if M['m00'] != 0:
                    center1 = (int(M['m10']/M['m00']), int(M['m01']/M['m00']))
                else:
                    continue
            
            # Check if too close to existing particles
            too_close = False
            for existing in filtered:
                if 'center' in existing:
                    center2 = existing['center']
                else:
                    M = cv2.moments(existing['contour'])
                    if M['m00'] != 0:
                        center2 = (int(M['m10']/M['m00']), int(M['m01']/M['m00']))
                    else:
                        continue
                
                distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
                if distance < 20:  # Minimum distance between particles
                    too_close = True
                    break
            
            if not too_close:
                filtered.append(particle)
        
        return filtered

    def _analyze_particle(self, particle, gray_image, particle_id):
        """Analyze individual particle properties"""
        try:
            contour = particle['contour']

            # Basic measurements
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            # Bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)

            # Centroid
            M = cv2.moments(contour)
            if M['m00'] != 0:
                cx = int(M['m10'] / M['m00'])
                cy = int(M['m01'] / M['m00'])
            else:
                cx, cy = x + w//2, y + h//2

            # Geometric properties
            aspect_ratio = float(w) / h if h != 0 else 0
            extent = float(area) / (w * h) if (w * h) != 0 else 0

            # Circularity
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter != 0 else 0

            # Equivalent diameter
            equiv_diameter = np.sqrt(4 * area / np.pi)

            # Solidity (convex hull)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = float(area) / hull_area if hull_area != 0 else 0

            # Major and minor axis lengths
            if len(contour) >= 5:
                ellipse = cv2.fitEllipse(contour)
                major_axis = max(ellipse[1])
                minor_axis = min(ellipse[1])
                orientation = ellipse[2]
            else:
                major_axis = max(w, h)
                minor_axis = min(w, h)
                orientation = 0

            # Texture analysis (extract region)
            mask = np.zeros(gray_image.shape, np.uint8)
            cv2.drawContours(mask, [contour], -1, 255, -1)

            # Get pixel values inside contour
            pixel_values = gray_image[mask == 255]

            # Texture properties
            if len(pixel_values) > 0:
                mean_intensity = np.mean(pixel_values)
                std_intensity = np.std(pixel_values)
                min_intensity = np.min(pixel_values)
                max_intensity = np.max(pixel_values)
            else:
                mean_intensity = std_intensity = min_intensity = max_intensity = 0

            # Classify particle type based on properties
            particle_type = self._classify_particle_type(area, aspect_ratio, circularity, solidity)

            return {
                'id': particle_id,
                'contour': contour,
                'center': (cx, cy),
                'bbox': (x, y, w, h),

                # Size measurements
                'area': area,
                'perimeter': perimeter,
                'equivalent_diameter': equiv_diameter,
                'major_axis': major_axis,
                'minor_axis': minor_axis,

                # Shape measurements
                'aspect_ratio': aspect_ratio,
                'circularity': circularity,
                'solidity': solidity,
                'extent': extent,
                'orientation': orientation,

                # Intensity measurements
                'mean_intensity': mean_intensity,
                'std_intensity': std_intensity,
                'min_intensity': min_intensity,
                'max_intensity': max_intensity,

                # Classification
                'particle_type': particle_type,
                'detection_method': particle.get('method', 'unknown')
            }

        except Exception as e:
            print(f"Error analyzing particle {particle_id}: {e}")
            return None

    def _classify_particle_type(self, area, aspect_ratio, circularity, solidity):
        """Classify particle type based on morphological properties for oil analysis"""

        # Oil particle type classification based on wear analysis standards
        # Size categories (in pixels, assuming 1 pixel ≈ 0.5 μm)
        if area < 30:  # < 15 μm²
            size_cat = "Fine"
        elif area < 100:  # 15-50 μm²
            size_cat = "Medium"
        elif area < 300:  # 50-150 μm²
            size_cat = "Large"
        else:  # > 150 μm²
            size_cat = "Coarse"

        # Particle type classification based on shape characteristics
        if circularity > 0.85 and solidity > 0.9:
            # Very round and solid - likely metallic wear particles
            particle_type = "Metallic Sphere"
        elif circularity > 0.7 and aspect_ratio < 1.5:
            # Round but less perfect - oxidized metal or debris
            particle_type = "Metal Debris"
        elif aspect_ratio > 4.0 and solidity > 0.8:
            # Very elongated and solid - cutting wear
            particle_type = "Cutting Wear"
        elif aspect_ratio > 2.5 and solidity > 0.7:
            # Elongated - sliding wear particles
            particle_type = "Sliding Wear"
        elif solidity < 0.6:
            # Low solidity - fibrous or organic contamination
            particle_type = "Fiber/Organic"
        elif circularity < 0.5 and solidity < 0.8:
            # Irregular shape - fatigue particles or severe wear
            particle_type = "Fatigue Wear"
        elif area > 200 and circularity < 0.6:
            # Large irregular - severe wear or contamination
            particle_type = "Severe Wear"
        else:
            # Default classification
            particle_type = "Normal Wear"

        return f"{size_cat} {particle_type}"

    def _create_visualization(self, original_image, particles):
        """Create visualization with detected particles colored by type"""
        if not particles:
            return original_image.copy()

        vis_image = original_image.copy()

        # Colors for different particle types (BGR format)
        type_colors = {
            'Metallic Sphere': (0, 255, 0),      # Green - normal metallic wear
            'Metal Debris': (0, 165, 255),       # Orange - oxidized metal
            'Cutting Wear': (0, 0, 255),         # Red - severe cutting wear
            'Sliding Wear': (255, 255, 0),       # Cyan - sliding wear
            'Fiber/Organic': (255, 0, 255),      # Magenta - contamination
            'Fatigue Wear': (0, 100, 255),       # Dark orange - fatigue
            'Severe Wear': (0, 0, 139),          # Dark red - severe wear
            'Normal Wear': (255, 255, 255),      # White - normal wear
        }

        for i, particle in enumerate(particles):
            particle_type = particle.get('particle_type', 'Normal Wear')

            # Get color based on particle type
            base_color = (0, 255, 0)  # Default green
            for type_key in type_colors:
                if type_key in particle_type:
                    base_color = type_colors[type_key]
                    break

            # Make color slightly transparent for better visibility
            color = base_color

            # Draw filled contour with transparency
            cv2.drawContours(vis_image, [particle['contour']], -1, color, 2)

            # Draw center point
            center = particle['center']
            cv2.circle(vis_image, center, 4, color, -1)

            # Draw particle ID with background
            text = str(particle['id'])
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 2

            # Get text size for background
            (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)

            # Draw text background
            cv2.rectangle(vis_image,
                         (center[0] + 5, center[1] - text_height - 5),
                         (center[0] + 5 + text_width, center[1] + 5),
                         (0, 0, 0), -1)

            # Draw text
            cv2.putText(vis_image, text,
                       (center[0] + 5, center[1] - 5),
                       font, font_scale, color, thickness)

            # Draw bounding box
            x, y, w, h = particle['bbox']
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 1)

        # Add legend to the image
        vis_image = self._add_legend(vis_image, particles)

        return vis_image

    def _add_legend(self, image, particles):
        """Add a legend showing particle types and their colors"""
        if not particles:
            return image

        # Get unique particle types in the image
        unique_types = set()
        for particle in particles:
            particle_type = particle.get('particle_type', 'Normal Wear')
            unique_types.add(particle_type)

        if not unique_types:
            return image

        # Colors for different particle types (BGR format)
        type_colors = {
            'Metallic Sphere': (0, 255, 0),      # Green
            'Metal Debris': (0, 165, 255),       # Orange
            'Cutting Wear': (0, 0, 255),         # Red
            'Sliding Wear': (255, 255, 0),       # Cyan
            'Fiber/Organic': (255, 0, 255),      # Magenta
            'Fatigue Wear': (0, 100, 255),       # Dark orange
            'Severe Wear': (0, 0, 139),          # Dark red
            'Normal Wear': (255, 255, 255),      # White
        }

        # Create legend
        legend_height = len(unique_types) * 25 + 20
        legend_width = 200

        # Position legend in top-right corner
        h, w = image.shape[:2]
        legend_x = w - legend_width - 10
        legend_y = 10

        # Draw legend background
        cv2.rectangle(image,
                     (legend_x, legend_y),
                     (legend_x + legend_width, legend_y + legend_height),
                     (0, 0, 0), -1)
        cv2.rectangle(image,
                     (legend_x, legend_y),
                     (legend_x + legend_width, legend_y + legend_height),
                     (255, 255, 255), 2)

        # Add legend title
        cv2.putText(image, "Particle Types:",
                   (legend_x + 5, legend_y + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Add legend entries
        y_offset = 35
        for particle_type in sorted(unique_types):
            # Get color for this type
            color = (255, 255, 255)  # Default white
            for type_key in type_colors:
                if type_key in particle_type:
                    color = type_colors[type_key]
                    break

            # Draw color box
            cv2.rectangle(image,
                         (legend_x + 5, legend_y + y_offset - 8),
                         (legend_x + 15, legend_y + y_offset + 2),
                         color, -1)

            # Draw type text (truncate if too long)
            display_text = particle_type
            if len(display_text) > 18:
                display_text = display_text[:15] + "..."

            cv2.putText(image, display_text,
                       (legend_x + 20, legend_y + y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            y_offset += 20

        return image

    def _create_summary(self, particles):
        """Create summary statistics"""
        if not particles:
            return {
                'total_particles': 0,
                'size_distribution': {},
                'shape_distribution': {},
                'average_size': 0,
                'size_range': (0, 0)
            }

        areas = [p['area'] for p in particles]
        types = [p['particle_type'] for p in particles]

        # Size statistics
        avg_area = np.mean(areas)
        min_area = np.min(areas)
        max_area = np.max(areas)

        # Size distribution
        small = len([a for a in areas if a < 50])
        medium = len([a for a in areas if 50 <= a < 200])
        large = len([a for a in areas if a >= 200])

        # Type distribution
        type_counts = {}
        for ptype in types:
            type_counts[ptype] = type_counts.get(ptype, 0) + 1

        return {
            'total_particles': len(particles),
            'size_distribution': {
                'small': small,
                'medium': medium,
                'large': large
            },
            'shape_distribution': type_counts,
            'average_size': avg_area,
            'size_range': (min_area, max_area),
            'total_area': sum(areas)
        }
