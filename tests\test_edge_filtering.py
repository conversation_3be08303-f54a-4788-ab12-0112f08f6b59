"""
Test Edge Artifact Filtering for Oil Particle Detection
Demonstrates removal of black edges and shadows from microscopy images
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from pathlib import Path

# Add utils to path
sys.path.append('utils')
from edge_artifact_filter import EdgeArtifactFilter

def create_test_image_with_artifacts():
    """Create a synthetic test image with edge artifacts"""
    
    # Create base image (simulating oil sample)
    height, width = 400, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 180  # Light gray background
    
    # Add some "real" particles in the center
    particles = [
        (150, 100, 30, 25),  # x, y, w, h
        (300, 200, 40, 35),
        (450, 150, 25, 30),
        (200, 300, 35, 40),
        (400, 280, 45, 25)
    ]
    
    for x, y, w, h in particles:
        # Draw particle as dark ellipse
        cv2.ellipse(image, (x + w//2, y + h//2), (w//2, h//2), 0, 0, 360, (60, 60, 60), -1)
        # Add some texture
        cv2.ellipse(image, (x + w//2, y + h//2), (w//4, h//4), 0, 0, 360, (40, 40, 40), -1)
    
    # Add edge artifacts (black edges/shadows)
    # Top edge shadow
    image[:20, :] = [10, 10, 10]
    
    # Bottom edge shadow
    image[-25:, :] = [15, 15, 15]
    
    # Left edge shadow
    image[:, :30] = [12, 12, 12]
    
    # Right edge shadow
    image[:, -35:] = [8, 8, 8]
    
    # Add some corner artifacts (very dark)
    image[:40, :40] = [5, 5, 5]  # Top-left
    image[:40, -40:] = [5, 5, 5]  # Top-right
    image[-40:, :40] = [5, 5, 5]  # Bottom-left
    image[-40:, -40:] = [5, 5, 5]  # Bottom-right
    
    # Add vignetting effect (gradual darkening towards edges)
    center_x, center_y = width // 2, height // 2
    y, x = np.ogrid[:height, :width]
    distances = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    max_distance = np.sqrt(center_x**2 + center_y**2)
    normalized_distances = distances / max_distance
    
    # Apply vignetting
    vignetting_factor = 1.0 - 0.3 * normalized_distances**2
    for c in range(3):
        image[:, :, c] = (image[:, :, c] * vignetting_factor).astype(np.uint8)
    
    return image

def simulate_detections_with_artifacts(image_shape):
    """Create simulated detections including some in artifact areas"""
    
    height, width = image_shape[:2]
    
    # Real particles (should be kept)
    real_detections = [
        {'bbox': [120, 80, 60, 50], 'confidence': 0.85, 'class_id': 1, 'class_name': 'Spherical'},
        {'bbox': [280, 180, 80, 70], 'confidence': 0.92, 'class_id': 2, 'class_name': 'Normal'},
        {'bbox': [430, 130, 50, 60], 'confidence': 0.78, 'class_id': 0, 'class_name': 'Lamellar'},
        {'bbox': [180, 280, 70, 80], 'confidence': 0.88, 'class_id': 4, 'class_name': 'Cutting'},
        {'bbox': [380, 260, 90, 50], 'confidence': 0.75, 'class_id': 3, 'class_name': 'Sliding'}
    ]
    
    # Artifact detections (should be filtered out)
    artifact_detections = [
        {'bbox': [5, 5, 30, 25], 'confidence': 0.65, 'class_id': 1, 'class_name': 'Spherical'},  # Top-left corner
        {'bbox': [width-40, 10, 35, 30], 'confidence': 0.70, 'class_id': 2, 'class_name': 'Normal'},  # Top-right edge
        {'bbox': [15, height-35, 25, 30], 'confidence': 0.60, 'class_id': 0, 'class_name': 'Lamellar'},  # Bottom-left edge
        {'bbox': [width-45, height-40, 40, 35], 'confidence': 0.68, 'class_name': 'Cutting'},  # Bottom-right corner
        {'bbox': [10, height//2, 20, 25], 'confidence': 0.55, 'class_id': 3, 'class_name': 'Sliding'}  # Left edge
    ]
    
    return real_detections + artifact_detections

def test_edge_filtering():
    """Test the edge artifact filtering system"""
    
    print("🧪 TESTING EDGE ARTIFACT FILTERING")
    print("="*50)
    
    # Create test image with artifacts
    print("📸 Creating test image with edge artifacts...")
    test_image = create_test_image_with_artifacts()
    
    # Create simulated detections
    print("🎯 Creating simulated detections (including artifacts)...")
    detections = simulate_detections_with_artifacts(test_image.shape)
    
    print(f"   Total detections: {len(detections)}")
    real_count = 5  # First 5 are real particles
    artifact_count = len(detections) - real_count
    print(f"   Real particles: {real_count}")
    print(f"   Artifact detections: {artifact_count}")
    
    # Initialize edge filter
    print("\n🔧 Initializing edge artifact filter...")
    edge_filter = EdgeArtifactFilter(
        edge_threshold=30,
        min_brightness=40,
        edge_margin=25,
        vignetting_detection=True
    )
    
    # Test filtering
    print("\n🔍 Testing edge artifact detection...")
    valid_mask = edge_filter.detect_edge_artifacts(test_image)
    
    print(f"   Valid area: {np.sum(valid_mask) / valid_mask.size * 100:.1f}%")
    print(f"   Artifact area: {(1 - np.sum(valid_mask) / valid_mask.size) * 100:.1f}%")
    
    # Filter detections
    print("\n🚫 Filtering detections...")
    filtered_detections = edge_filter.filter_detections(
        detections, 
        test_image.shape, 
        valid_mask
    )
    
    filtered_count = len(detections) - len(filtered_detections)
    print(f"   Original detections: {len(detections)}")
    print(f"   Filtered detections: {len(filtered_detections)}")
    print(f"   Removed: {filtered_count}")
    
    # Visualize results
    print("\n📊 Creating visualization...")
    visualize_filtering_results(test_image, detections, filtered_detections, valid_mask)
    
    # Test with real image if available
    test_real_image()
    
    return {
        'original_detections': len(detections),
        'filtered_detections': len(filtered_detections),
        'removed_artifacts': filtered_count,
        'valid_area_percentage': np.sum(valid_mask) / valid_mask.size * 100
    }

def visualize_filtering_results(image, original_detections, filtered_detections, valid_mask):
    """Visualize the filtering results"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Original image with all detections
    ax1 = axes[0, 0]
    ax1.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    for det in original_detections:
        x, y, w, h = det['bbox']
        rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='red', facecolor='none')
        ax1.add_patch(rect)
        ax1.text(x, y-5, f"{det.get('class_name', 'Unknown')}", color='red', fontsize=8)
    ax1.set_title(f'Original Detections ({len(original_detections)})')
    ax1.axis('off')
    
    # Valid area mask
    ax2 = axes[0, 1]
    ax2.imshow(valid_mask, cmap='RdYlGn')
    ax2.set_title('Valid Area Mask\n(Green=Valid, Red=Artifact)')
    ax2.axis('off')
    
    # Filtered detections
    ax3 = axes[1, 0]
    ax3.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    for det in filtered_detections:
        x, y, w, h = det['bbox']
        rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='green', facecolor='none')
        ax3.add_patch(rect)
        ax3.text(x, y-5, f"{det.get('class_name', 'Unknown')}", color='green', fontsize=8)
    ax3.set_title(f'Filtered Detections ({len(filtered_detections)})')
    ax3.axis('off')
    
    # Overlay comparison
    ax4 = axes[1, 1]
    overlay = image.copy()
    overlay[~valid_mask] = [255, 0, 0]  # Red for invalid areas
    ax4.imshow(cv2.cvtColor(overlay, cv2.COLOR_BGR2RGB))
    ax4.set_title('Image + Invalid Areas (Red)')
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('edge_filtering_test_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Visualization saved: edge_filtering_test_results.png")

def test_real_image():
    """Test with real oil particle image if available"""
    
    print("\n🔍 Testing with real images...")
    
    # Look for sample images
    sample_paths = [
        "data/samples/sample1.jpg",
        "data/samples/sample2.jpg",
        "02_DATA/raw_images/image1.jpg",
        "02_DATA/processed/image1.jpg"
    ]
    
    for sample_path in sample_paths:
        if os.path.exists(sample_path):
            print(f"   Found real image: {sample_path}")
            
            # Load image
            real_image = cv2.imread(sample_path)
            if real_image is not None:
                # Test filtering
                edge_filter = EdgeArtifactFilter()
                edge_filter.visualize_filtering(
                    real_image,
                    save_path=f"real_image_filtering_{Path(sample_path).stem}.png"
                )
                return
    
    print("   No real images found for testing")
    print("   Place test images in data/samples/ to test with real data")

def main():
    """Main test function"""
    
    print("🚀 EDGE ARTIFACT FILTERING TEST")
    print("="*50)
    print("This test demonstrates filtering of black edges and shadows")
    print("that are common artifacts in microscopy images.")
    print()
    
    # Run test
    results = test_edge_filtering()
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("-"*30)
    print(f"Original detections: {results['original_detections']}")
    print(f"Filtered detections: {results['filtered_detections']}")
    print(f"Removed artifacts: {results['removed_artifacts']}")
    print(f"Valid area: {results['valid_area_percentage']:.1f}%")
    
    efficiency = results['removed_artifacts'] / results['original_detections'] * 100
    print(f"Filtering efficiency: {efficiency:.1f}%")
    
    print("\n✅ Edge artifact filtering test completed!")
    print("   Check 'edge_filtering_test_results.png' for visualization")
    
    return results

if __name__ == '__main__':
    main()
