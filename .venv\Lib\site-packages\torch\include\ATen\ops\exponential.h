#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/exponential_ops.h>

namespace at {


// aten::exponential.out(Tensor self, float lambd=1, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & exponential_out(at::Tensor & out, const at::Tensor & self, double lambd=1, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::exponential_out::call(self, lambd, generator, out);
}
// aten::exponential.out(Tensor self, float lambd=1, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & exponential_outf(const at::Tensor & self, double lambd, ::std::optional<at::Generator> generator, at::Tensor & out) {
    return at::_ops::exponential_out::call(self, lambd, generator, out);
}

// aten::exponential(Tensor self, float lambd=1, *, Generator? generator=None) -> Tensor
inline at::Tensor exponential(const at::Tensor & self, double lambd=1, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::exponential::call(self, lambd, generator);
}

}
