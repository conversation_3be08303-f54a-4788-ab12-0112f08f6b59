{"timestamp": "2025-07-07 15:45:03", "system_info": {"python_version": "3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]", "pytorch_version": "2.7.1+cpu", "cuda_available": false, "device": "cpu"}, "resnet50d_evaluation": {"model_type": "ResNet50d", "num_classes": 3, "classes": ["Early Wear", "Middle Wear", "Late Wear"], "training_epochs": 5, "reported_accuracy": 0.80357, "model_loaded": true, "total_parameters": 23533411, "trainable_parameters": 23533411, "model_size_mb": 89.7728385925293}, "transformer_evaluation": {"model_name": "facebook/detr-resnet-50", "model_type": "DETR ResNet-50", "total_parameters": 41524768, "trainable_parameters": 41302368, "model_size_mb": 158.4044189453125, "device": "cpu", "confidence_threshold": 0.5, "architecture": "Transformer-based Object Detection", "simulation_mode": false}, "rt_detr_evaluation": {"model_type": "RT-DETR", "backbone": "ResNet50", "model_loaded": true, "confidence_threshold": 0.5, "particle_classes": ["normal_particle", "cutting_particle", "sliding_particle", "spherical_particle", "lamellar_particle", "fatigue_particle", "wear_particle"], "total_parameters": 41524768, "model_size_mb": 158.4044189453125}, "speed_benchmark": {"RT-DETR": {"time_seconds": 1.1621730327606201, "fps": 0.8604570677609038, "detections_count": 0}, "Practical_CV": {"time_seconds": 3.515216588973999, "fps": 0.28447749226509944, "detections_count": 550}}, "wear_classification_test": {"test_results": [{"expected": "early", "predicted": "Late Wear", "confidence": 0.5309127569198608}, {"expected": "middle", "predicted": "Late Wear", "confidence": 0.9946717619895935}, {"expected": "late", "predicted": "Late Wear", "confidence": 0.9999995231628418}], "model_available": true}, "loss_analysis": {"Transformer_Detection": {"loss_components": ["Classification Loss: Focal/Cross Entropy for object classification", "Regression Loss: L1 Loss for bounding box coordinates", "Hungarian Matching: Optimal assignment between predictions and targets"], "loss_functions": {"classification": "Focal Loss / Cross Entropy", "regression": "L1 Loss (smooth)", "matching": "Hungarian algorithm for bipartite matching"}, "total_loss": "weighted_sum(class_loss + bbox_loss + giou_loss)", "advantages": ["End-to-end differentiable", "No anchor boxes required", "Global optimization via set prediction"]}, "ResNet50d": {"loss_function": "Cross Entropy Loss", "purpose": "Multi-class wear stage classification", "classes": 3, "optimization": "Likely Adam or SGD optimizer"}, "RT-DETR": {"loss_components": ["Classification Loss: Focal Loss or Cross Entropy", "Regression Loss: L1 Loss for bounding boxes", "Hungarian Matching: Bipartite matching loss"], "architecture": "Transformer-based with DETR-style losses"}}}