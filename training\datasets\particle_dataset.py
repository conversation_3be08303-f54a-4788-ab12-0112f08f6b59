"""
Particle Detection Dataset for Training Transformer Models
Supports COCO format annotations and advanced augmentations
"""

import torch
from torch.utils.data import Dataset
import cv2
import numpy as np
import json
import os
from PIL import Image
from typing import Dict, List, Optional, Tuple, Any
import albumentations as A
from albumentations.pytorch import ToTensorV2
import logging

logger = logging.getLogger(__name__)


class ParticleDetectionDataset(Dataset):
    """
    Dataset class for oil particle detection training
    Supports COCO format annotations with particle-specific augmentations
    """
    
    def __init__(self, 
                 data_dir: str,
                 processor: Optional[Any] = None,
                 augment: bool = True,
                 max_size: int = 800,
                 min_size: int = 480):
        """
        Args:
            data_dir: Directory containing images and annotations
            processor: HuggingFace image processor
            augment: Whether to apply data augmentation
            max_size: Maximum image size for resizing
            min_size: Minimum image size for resizing
        """
        self.data_dir = data_dir
        self.processor = processor
        self.augment = augment
        self.max_size = max_size
        self.min_size = min_size
        
        # Load annotations
        self.annotations_file = os.path.join(data_dir, 'annotations.json')
        self.images_dir = os.path.join(data_dir, 'images')
        
        if os.path.exists(self.annotations_file):
            with open(self.annotations_file, 'r') as f:
                self.coco_data = json.load(f)
            self._parse_annotations()
        else:
            logger.warning(f"No annotations file found at {self.annotations_file}")
            self.image_files = self._get_image_files()
            self.annotations = {}
        
        # Setup augmentations
        self.transform = self._setup_augmentations()
        
        # Particle class mapping
        self.class_names = [
            'background',
            'spherical_particle',
            'irregular_particle', 
            'elongated_particle',
            'cluster_particle',
            'micro_particle'
        ]
        self.num_classes = len(self.class_names)
    
    def _parse_annotations(self):
        """Parse COCO format annotations"""
        self.images = {img['id']: img for img in self.coco_data['images']}
        self.categories = {cat['id']: cat for cat in self.coco_data['categories']}
        
        # Group annotations by image
        self.annotations = {}
        for ann in self.coco_data['annotations']:
            image_id = ann['image_id']
            if image_id not in self.annotations:
                self.annotations[image_id] = []
            self.annotations[image_id].append(ann)
        
        self.image_ids = list(self.images.keys())
        logger.info(f"Loaded {len(self.image_ids)} images with annotations")
    
    def _get_image_files(self):
        """Get image files when no annotations are available"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        if os.path.exists(self.images_dir):
            for file in os.listdir(self.images_dir):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    image_files.append(file)
        
        logger.info(f"Found {len(image_files)} images without annotations")
        return image_files
    
    def _setup_augmentations(self):
        """Setup data augmentation pipeline"""
        from augmentations.particle_augmentations import get_advanced_particle_augmentations

        return get_advanced_particle_augmentations(
            image_size=self.max_size,
            training=self.augment
        )
    
    def __len__(self):
        """Return dataset length"""
        if hasattr(self, 'image_ids'):
            return len(self.image_ids)
        else:
            return len(self.image_files)
    
    def __getitem__(self, idx):
        """Get dataset item"""
        if hasattr(self, 'image_ids'):
            return self._get_annotated_item(idx)
        else:
            return self._get_unannotated_item(idx)
    
    def _get_annotated_item(self, idx):
        """Get item with annotations"""
        image_id = self.image_ids[idx]
        image_info = self.images[image_id]
        
        # Load image
        image_path = os.path.join(self.images_dir, image_info['file_name'])
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Get annotations for this image
        image_annotations = self.annotations.get(image_id, [])
        
        # Extract bounding boxes and labels
        bboxes = []
        class_labels = []
        
        for ann in image_annotations:
            bbox = ann['bbox']  # COCO format: [x, y, width, height]
            category_id = ann['category_id']
            
            bboxes.append(bbox)
            class_labels.append(category_id)
        
        # Apply augmentations
        if len(bboxes) > 0:
            transformed = self.transform(
                image=image,
                bboxes=bboxes,
                class_labels=class_labels
            )
            
            image = transformed['image']
            bboxes = transformed['bboxes']
            class_labels = transformed['class_labels']
        else:
            # No annotations - just transform image
            transformed = A.Compose([
                A.Resize(height=self.max_size, width=self.max_size),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])(image=image)
            image = transformed['image']
        
        # Convert to DETR format
        target = self._format_target(bboxes, class_labels, image.shape[-2:])
        
        return image, target
    
    def _get_unannotated_item(self, idx):
        """Get item without annotations (for inference)"""
        image_file = self.image_files[idx]
        image_path = os.path.join(self.images_dir, image_file)
        
        # Load image
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Apply basic transforms
        transformed = A.Compose([
            A.Resize(height=self.max_size, width=self.max_size),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])(image=image)
        
        image = transformed['image']
        
        # Empty target for inference
        target = {
            'labels': torch.tensor([], dtype=torch.long),
            'boxes': torch.tensor([], dtype=torch.float32).reshape(0, 4)
        }
        
        return image, target
    
    def _format_target(self, bboxes: List, class_labels: List, image_size: Tuple[int, int]) -> Dict:
        """Format target for DETR training"""
        if len(bboxes) == 0:
            return {
                'labels': torch.tensor([], dtype=torch.long),
                'boxes': torch.tensor([], dtype=torch.float32).reshape(0, 4)
            }
        
        # Convert COCO format to normalized center format
        height, width = image_size
        boxes = []
        
        for bbox in bboxes:
            x, y, w, h = bbox
            # Convert to center format and normalize
            center_x = (x + w / 2) / width
            center_y = (y + h / 2) / height
            norm_w = w / width
            norm_h = h / height
            
            boxes.append([center_x, center_y, norm_w, norm_h])
        
        return {
            'labels': torch.tensor(class_labels, dtype=torch.long),
            'boxes': torch.tensor(boxes, dtype=torch.float32)
        }
    
    def collate_fn(self, batch):
        """Custom collate function for batch processing"""
        images = []
        targets = []
        
        for image, target in batch:
            images.append(image)
            targets.append(target)
        
        # Stack images
        images = torch.stack(images)
        
        return images, targets


def create_particle_dataset(data_dir: str, 
                          processor: Optional[Any] = None,
                          **kwargs) -> ParticleDetectionDataset:
    """Factory function to create particle dataset"""
    return ParticleDetectionDataset(
        data_dir=data_dir,
        processor=processor,
        **kwargs
    )
