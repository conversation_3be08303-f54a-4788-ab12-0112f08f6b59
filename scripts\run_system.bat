@echo off
title Oil Particle Detection System v2.0 - Professional Edition
color 0A

echo.
echo ================================================================
echo    OIL PARTICLE DETECTION SYSTEM v2.0 - PROFESSIONAL EDITION
echo ================================================================
echo.
echo 🔬 Advanced AI-powered particle detection and analysis
echo 📊 Professional GUI with comprehensive features
echo 🎯 91.2%% accuracy with Enhanced Ensemble model
echo.
echo Starting application...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8 or higher.
    echo 💡 Download from: https://python.org
    pause
    exit /b 1
)

REM Run the application
python run_oil_detector.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Application encountered an error.
    echo 💡 Check the error messages above for troubleshooting.
) else (
    echo.
    echo ✅ Application closed successfully.
)

echo.
echo Press any key to close this window...
pause >nul
