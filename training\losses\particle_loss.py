"""
Custom Loss Functions for Oil Particle Detection
Optimized for small object detection and particle-specific characteristics
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import numpy as np


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance in object detection
    Particularly useful for particle detection where background dominates
    """
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: Predicted logits [N, C]
            targets: Ground truth labels [N]
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class IoULoss(nn.Module):
    """
    IoU-based losses for better bounding box regression
    Supports IoU, GIoU, DIoU, and CIoU variants
    """
    
    def __init__(self, loss_type: str = 'giou', reduction: str = 'mean'):
        super().__init__()
        self.loss_type = loss_type.lower()
        self.reduction = reduction
        
        if self.loss_type not in ['iou', 'giou', 'diou', 'ciou']:
            raise ValueError(f"Unsupported IoU loss type: {loss_type}")
    
    def forward(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred_boxes: Predicted boxes [N, 4] in (x1, y1, x2, y2) format
            target_boxes: Target boxes [N, 4] in (x1, y1, x2, y2) format
        """
        if self.loss_type == 'iou':
            loss = 1 - self._calculate_iou(pred_boxes, target_boxes)
        elif self.loss_type == 'giou':
            loss = 1 - self._calculate_giou(pred_boxes, target_boxes)
        elif self.loss_type == 'diou':
            loss = 1 - self._calculate_diou(pred_boxes, target_boxes)
        elif self.loss_type == 'ciou':
            loss = 1 - self._calculate_ciou(pred_boxes, target_boxes)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss
    
    def _calculate_iou(self, box1: torch.Tensor, box2: torch.Tensor) -> torch.Tensor:
        """Calculate IoU between two sets of boxes"""
        # Calculate intersection
        inter_x1 = torch.max(box1[:, 0], box2[:, 0])
        inter_y1 = torch.max(box1[:, 1], box2[:, 1])
        inter_x2 = torch.min(box1[:, 2], box2[:, 2])
        inter_y2 = torch.min(box1[:, 3], box2[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        # Calculate union
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - inter_area
        
        # Calculate IoU
        iou = inter_area / (union_area + 1e-8)
        return iou
    
    def _calculate_giou(self, box1: torch.Tensor, box2: torch.Tensor) -> torch.Tensor:
        """Calculate Generalized IoU"""
        iou = self._calculate_iou(box1, box2)
        
        # Calculate enclosing box
        enclose_x1 = torch.min(box1[:, 0], box2[:, 0])
        enclose_y1 = torch.min(box1[:, 1], box2[:, 1])
        enclose_x2 = torch.max(box1[:, 2], box2[:, 2])
        enclose_y2 = torch.max(box1[:, 3], box2[:, 3])
        
        enclose_area = (enclose_x2 - enclose_x1) * (enclose_y2 - enclose_y1)
        
        # Calculate union area
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - iou * box1_area  # Approximate union
        
        # Calculate GIoU
        giou = iou - (enclose_area - union_area) / (enclose_area + 1e-8)
        return giou
    
    def _calculate_diou(self, box1: torch.Tensor, box2: torch.Tensor) -> torch.Tensor:
        """Calculate Distance IoU"""
        iou = self._calculate_iou(box1, box2)
        
        # Calculate center points
        box1_center_x = (box1[:, 0] + box1[:, 2]) / 2
        box1_center_y = (box1[:, 1] + box1[:, 3]) / 2
        box2_center_x = (box2[:, 0] + box2[:, 2]) / 2
        box2_center_y = (box2[:, 1] + box2[:, 3]) / 2
        
        # Calculate center distance
        center_distance = (box1_center_x - box2_center_x) ** 2 + (box1_center_y - box2_center_y) ** 2
        
        # Calculate diagonal distance of enclosing box
        enclose_x1 = torch.min(box1[:, 0], box2[:, 0])
        enclose_y1 = torch.min(box1[:, 1], box2[:, 1])
        enclose_x2 = torch.max(box1[:, 2], box2[:, 2])
        enclose_y2 = torch.max(box1[:, 3], box2[:, 3])
        
        diagonal_distance = (enclose_x2 - enclose_x1) ** 2 + (enclose_y2 - enclose_y1) ** 2
        
        # Calculate DIoU
        diou = iou - center_distance / (diagonal_distance + 1e-8)
        return diou
    
    def _calculate_ciou(self, box1: torch.Tensor, box2: torch.Tensor) -> torch.Tensor:
        """Calculate Complete IoU"""
        diou = self._calculate_diou(box1, box2)
        
        # Calculate aspect ratio consistency
        box1_w = box1[:, 2] - box1[:, 0]
        box1_h = box1[:, 3] - box1[:, 1]
        box2_w = box2[:, 2] - box2[:, 0]
        box2_h = box2[:, 3] - box2[:, 1]
        
        v = (4 / (np.pi ** 2)) * torch.pow(torch.atan(box1_w / (box1_h + 1e-8)) - torch.atan(box2_w / (box2_h + 1e-8)), 2)
        
        iou = self._calculate_iou(box1, box2)
        alpha = v / (1 - iou + v + 1e-8)
        
        # Calculate CIoU
        ciou = diou - alpha * v
        return ciou


class SizeAwareLoss(nn.Module):
    """
    Size-aware loss for better detection of small particles
    Gives higher weight to smaller objects
    """
    
    def __init__(self, size_threshold: float = 32.0, small_weight: float = 2.0):
        super().__init__()
        self.size_threshold = size_threshold
        self.small_weight = small_weight
    
    def forward(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred_boxes: Predicted boxes [N, 4]
            target_boxes: Target boxes [N, 4]
        """
        # Calculate box areas
        target_areas = (target_boxes[:, 2] - target_boxes[:, 0]) * (target_boxes[:, 3] - target_boxes[:, 1])
        
        # Create size-based weights
        weights = torch.ones_like(target_areas)
        small_mask = target_areas < (self.size_threshold ** 2)
        weights[small_mask] = self.small_weight
        
        # Calculate L1 loss with size-aware weighting
        l1_loss = F.l1_loss(pred_boxes, target_boxes, reduction='none').mean(dim=1)
        weighted_loss = l1_loss * weights
        
        return weighted_loss.mean()


class ParticleDetectionLoss(nn.Module):
    """
    Combined loss function for particle detection
    Integrates classification, regression, and particle-specific losses
    """
    
    def __init__(self, 
                 focal_alpha: float = 0.25,
                 focal_gamma: float = 2.0,
                 iou_loss_weight: float = 1.0,
                 size_loss_weight: float = 0.5,
                 iou_loss_type: str = 'giou'):
        super().__init__()
        
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.iou_loss = IoULoss(loss_type=iou_loss_type)
        self.size_loss = SizeAwareLoss()
        
        self.iou_loss_weight = iou_loss_weight
        self.size_loss_weight = size_loss_weight
    
    def forward(self, outputs: Dict, targets: List[Dict]) -> torch.Tensor:
        """
        Args:
            outputs: Model outputs containing logits and pred_boxes
            targets: List of target dictionaries with labels and boxes
        """
        # Extract predictions
        pred_logits = outputs.logits  # [batch_size, num_queries, num_classes]
        pred_boxes = outputs.pred_boxes  # [batch_size, num_queries, 4]
        
        # Prepare targets (this is simplified - actual implementation would need Hungarian matching)
        batch_size = pred_logits.shape[0]
        device = pred_logits.device
        
        total_loss = 0.0
        
        for i in range(batch_size):
            if len(targets) > i and 'labels' in targets[i]:
                target_labels = targets[i]['labels'].to(device)
                target_boxes = targets[i]['boxes'].to(device)
                
                # Classification loss (simplified - would need proper matching)
                if len(target_labels) > 0:
                    # Take first few predictions for simplicity
                    num_targets = min(len(target_labels), pred_logits.shape[1])
                    cls_loss = self.focal_loss(
                        pred_logits[i, :num_targets], 
                        target_labels[:num_targets]
                    )
                    
                    # Regression losses
                    if len(target_boxes) > 0:
                        reg_loss = self.iou_loss(
                            pred_boxes[i, :num_targets], 
                            target_boxes[:num_targets]
                        )
                        size_loss = self.size_loss(
                            pred_boxes[i, :num_targets], 
                            target_boxes[:num_targets]
                        )
                        
                        total_loss += cls_loss + self.iou_loss_weight * reg_loss + self.size_loss_weight * size_loss
                    else:
                        total_loss += cls_loss
        
        return total_loss / batch_size if batch_size > 0 else torch.tensor(0.0, device=device)
