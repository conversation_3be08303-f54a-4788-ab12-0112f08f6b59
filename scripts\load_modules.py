#!/usr/bin/env python3
"""
Module Loader for Oil Particle Detection System
Ensures all required modules are properly loaded and available
"""

import sys
import os
import importlib
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModuleLoader:
    """Handles loading and validation of detection modules"""
    
    def __init__(self):
        self.loaded_modules = {}
        self.module_status = {}
        self.setup_paths()
    
    def setup_paths(self):
        """Setup Python paths for module imports"""
        current_dir = Path(__file__).parent
        
        # Add src directory to path
        src_path = current_dir / "src"
        if src_path.exists():
            sys.path.insert(0, str(src_path))
            logger.info(f"Added {src_path} to Python path")
        
        # Add app directory to path
        app_path = current_dir / "app"
        if app_path.exists():
            sys.path.insert(0, str(app_path))
            logger.info(f"Added {app_path} to Python path")
    
    def load_detection_modules(self):
        """Load all detection modules"""
        modules_to_load = {
            'practical_detector': 'models.practical_detector.PracticalParticleDetector',
            'rt_detr_detector': 'models.rt_detr_detector.RTDETRParticleDetector',
            'transformer_detector': 'models.transformer_detector.TransformerParticleDetector',
            'particle_analyzer': 'models.particle_analyzer.ParticleAnalyzer'
        }
        
        for module_name, module_path in modules_to_load.items():
            try:
                self.load_module(module_name, module_path)
            except Exception as e:
                logger.warning(f"Failed to load {module_name}: {e}")
                self.module_status[module_name] = f"Failed: {e}"
    
    def load_module(self, name, module_path):
        """Load a specific module"""
        try:
            module_parts = module_path.split('.')
            class_name = module_parts[-1]
            module_path = '.'.join(module_parts[:-1])
            
            # Import the module
            module = importlib.import_module(module_path)
            
            # Get the class
            cls = getattr(module, class_name)
            
            self.loaded_modules[name] = cls
            self.module_status[name] = "Loaded successfully"
            logger.info(f"✅ {name} loaded successfully")
            
        except ImportError as e:
            logger.error(f"❌ Import error for {name}: {e}")
            self.module_status[name] = f"Import error: {e}"
            raise
        except AttributeError as e:
            logger.error(f"❌ Class not found for {name}: {e}")
            self.module_status[name] = f"Class not found: {e}"
            raise
    
    def get_module(self, name):
        """Get a loaded module"""
        return self.loaded_modules.get(name)
    
    def get_status(self):
        """Get loading status of all modules"""
        return self.module_status.copy()
    
    def print_status(self):
        """Print the status of all modules"""
        print("\n" + "="*50)
        print("MODULE LOADING STATUS")
        print("="*50)
        
        for module_name, status in self.module_status.items():
            status_icon = "✅" if "successfully" in status else "❌"
            print(f"{status_icon} {module_name}: {status}")
        
        print("="*50)
    
    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        required_packages = [
            'PyQt5', 'cv2', 'numpy', 'matplotlib', 'scipy', 
            'skimage', 'PIL', 'torch', 'transformers'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'cv2':
                    import cv2
                elif package == 'skimage':
                    import skimage
                elif package == 'PIL':
                    import PIL
                else:
                    importlib.import_module(package)
                logger.info(f"✅ {package} is available")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"❌ {package} is missing")
        
        return missing_packages

def main():
    """Main function to load and test modules"""
    print("Oil Particle Detection System - Module Loader")
    print("=" * 50)
    
    loader = ModuleLoader()
    
    # Check dependencies
    print("\nChecking dependencies...")
    missing = loader.check_dependencies()
    
    if missing:
        print(f"\n⚠️  Missing packages: {', '.join(missing)}")
        print("Please install missing packages using:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    # Load detection modules
    print("\nLoading detection modules...")
    loader.load_detection_modules()
    
    # Print status
    loader.print_status()
    
    # Test basic functionality
    print("\nTesting basic functionality...")
    practical_detector = loader.get_module('practical_detector')
    if practical_detector:
        try:
            detector = practical_detector()
            print("✅ Practical detector instantiated successfully")
        except Exception as e:
            print(f"❌ Failed to instantiate practical detector: {e}")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 All modules loaded successfully!")
        print("You can now run the oil particle detection application.")
    else:
        print("\n❌ Module loading failed. Please check the errors above.")
