Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: scikit-image in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (0.25.2)
Requirement already satisfied: numpy>=1.24 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (1.26.4)
Requirement already satisfied: scipy>=1.11.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (1.16.0)
Requirement already satisfied: networkx>=3.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (3.5)
Requirement already satisfied: pillow>=10.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (11.2.1)
Requirement already satisfied: imageio!=2.35.0,>=2.33 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (2.37.0)
Requirement already satisfied: tifffile>=2022.8.12 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (2025.6.11)
Requirement already satisfied: packaging>=21 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (25.0)
Requirement already satisfied: lazy-loader>=0.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.12_qbz5n2kfra8p0\localcache\local-packages\python312\site-packages (from scikit-image) (0.4)
