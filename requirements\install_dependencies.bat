@echo off
echo Installing RT-DETR and Oil Particle Detection Dependencies...
echo.

REM Upgrade pip first
python -m pip install --upgrade pip

echo Installing core dependencies...
pip install PyQt5>=5.15.0
pip install opencv-python>=4.8.0
pip install Pillow>=10.0.0
pip install scikit-image>=0.20.0

echo Installing scientific computing libraries...
pip install numpy>=1.24.0
pip install pandas>=2.0.0
pip install matplotlib>=3.7.0
pip install scipy>=1.10.0

echo Installing PyTorch (CPU version for compatibility)...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

echo Installing Transformers and Hugging Face libraries...
pip install transformers>=4.30.0
pip install accelerate
pip install datasets

echo Installing RT-DETR specific dependencies...
pip install ultralytics>=8.0.0
pip install supervision>=0.16.0
pip install timm>=0.9.0

echo Installing additional ML libraries...
pip install albumentations>=1.3.0
pip install tqdm>=4.65.0
pip install pyyaml>=6.0
pip install requests>=2.31.0
pip install omegaconf>=2.3.0

echo.
echo Installation completed!
echo You can now run the oil particle detector with RT-DETR support.
echo.
pause
