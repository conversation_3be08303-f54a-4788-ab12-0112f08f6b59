"""
Modern Transformer-Based Object Detection for Oil Particle Detection
Replaces YOLOv3 with state-of-the-art transformer models like DINO, DETR variants
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
from PIL import Image
from typing import List, Dict, Optional, Tuple
import logging
import sys
import os
from transformers import (
    DetrImageProcessor, DetrForObjectDetection,
    AutoImageProcessor, AutoModelForObjectDetection
)

# Add utils to path for edge artifact filter
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))
from edge_artifact_filter import EdgeArtifactFilter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TransformerParticleDetector:
    """
    Modern transformer-based particle detector replacing YOLOv3
    Supports multiple transformer architectures: DINO, DETR, Conditional DETR, etc.
    """
    
    def __init__(self,
                 model_name: str = "facebook/detr-resnet-50",
                 confidence_threshold: float = 0.5,
                 device: str = None,
                 filter_edge_artifacts: bool = True):
        """
        Initialize transformer-based detector

        Args:
            model_name: HuggingFace model identifier
            confidence_threshold: Minimum confidence for detections
            device: Device to run inference on
            filter_edge_artifacts: Whether to filter out edge artifacts and black shadows
        """
        self.model_name = model_name
        self.confidence_threshold = confidence_threshold
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.filter_edge_artifacts = filter_edge_artifacts

        # Model components
        self.model = None
        self.processor = None

        # Edge artifact filter for removing black edges/shadows
        if self.filter_edge_artifacts:
            self.edge_filter = EdgeArtifactFilter(
                edge_threshold=30,      # Detect dark edges
                min_brightness=40,      # Minimum brightness for valid particles
                edge_margin=25,         # Exclude edge margins
                vignetting_detection=True  # Detect lens vignetting
            )
        else:
            self.edge_filter = None
        
        # Supported transformer models
        self.supported_models = {
            "facebook/detr-resnet-50": "DETR ResNet-50",
            "facebook/detr-resnet-101": "DETR ResNet-101", 
            "microsoft/conditional-detr-resnet-50": "Conditional DETR",
            "hustvl/yolos-tiny": "YOLOS Tiny",
            "hustvl/yolos-small": "YOLOS Small",
            "facebook/dino-detr-resnet-50": "DINO DETR (if available)"
        }
        
        # Load model
        self.load_model()
        
    def load_model(self):
        """Load transformer model and processor"""
        try:
            logger.info(f"Loading transformer model: {self.model_name}")
            
            # Load processor for image preprocessing
            try:
                self.processor = AutoImageProcessor.from_pretrained(self.model_name)
            except:
                # Fallback to DETR processor
                self.processor = DetrImageProcessor.from_pretrained(self.model_name)
            
            # Load model
            try:
                self.model = AutoModelForObjectDetection.from_pretrained(self.model_name)
            except:
                # Fallback to DETR model
                self.model = DetrForObjectDetection.from_pretrained(self.model_name)
                
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"Transformer model loaded successfully on {self.device}")
            logger.info(f"Model type: {self.supported_models.get(self.model_name, 'Custom Model')}")
            
        except Exception as e:
            logger.error(f"Failed to load transformer model: {e}")
            logger.warning("Model loading failed - detector will run in simulation mode")
            self.model = None
            self.processor = None
    
    def get_model_info(self) -> Dict:
        """Get detailed model information"""
        if self.model is None:
            return {"error": "Model not loaded", "simulation_mode": True}
            
        try:
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            return {
                "model_name": self.model_name,
                "model_type": self.supported_models.get(self.model_name, "Custom Transformer"),
                "total_parameters": total_params,
                "trainable_parameters": trainable_params,
                "model_size_mb": total_params * 4 / (1024 * 1024),
                "device": str(self.device),
                "confidence_threshold": self.confidence_threshold,
                "architecture": "Transformer-based Object Detection",
                "simulation_mode": False
            }
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {"error": str(e), "simulation_mode": True}
    
    def preprocess_image(self, image: np.ndarray) -> Dict:
        """
        Preprocess image for transformer model
        
        Args:
            image: Input image as numpy array (BGR format from OpenCV)
            
        Returns:
            Preprocessed inputs for model
        """
        if self.processor is None:
            raise RuntimeError("Processor not loaded")
            
        # Convert BGR to RGB
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
            
        # Convert to PIL Image
        pil_image = Image.fromarray(image_rgb)
        
        # Process with transformer processor
        inputs = self.processor(images=pil_image, return_tensors="pt")
        
        # Move to device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        return inputs
    
    def postprocess_outputs(self, outputs, image_shape: Tuple[int, int, int]) -> List[Dict]:
        """
        Post-process model outputs to extract particle detections
        
        Args:
            outputs: Raw model outputs
            image_shape: Original image shape (H, W, C)
            
        Returns:
            List of detected particles with bounding boxes and classifications
        """
        if self.processor is None:
            return []
            
        try:
            # Get image dimensions
            height, width = image_shape[:2]
            
            # Post-process with processor
            results = self.processor.post_process_object_detection(
                outputs, 
                threshold=self.confidence_threshold,
                target_sizes=torch.tensor([[height, width]])
            )[0]
            
            detections = []
            
            for score, label, box in zip(results["scores"], results["labels"], results["boxes"]):
                # Convert box coordinates
                x1, y1, x2, y2 = box.cpu().numpy()
                
                # Calculate particle properties
                width_px = x2 - x1
                height_px = y2 - y1
                area_px = width_px * height_px
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                detection = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'confidence': float(score.cpu()),
                    'class_id': int(label.cpu()),
                    'class_name': f"particle_class_{int(label.cpu())}",
                    'center': [float(center_x), float(center_y)],
                    'dimensions': {
                        'width_px': float(width_px),
                        'height_px': float(height_px),
                        'area_px': float(area_px)
                    },
                    'detection_method': 'transformer'
                }
                
                detections.append(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"Post-processing failed: {e}")
            return []
    
    def detect_particles(self, image: np.ndarray) -> List[Dict]:
        """
        Detect oil particles using transformer model

        Args:
            image: Input image as numpy array

        Returns:
            List of detected particles with bounding boxes and classifications
        """
        if self.model is None:
            # Simulation mode
            detections = self._simulate_detection(image)
        else:
            try:
                # Preprocess image
                inputs = self.preprocess_image(image)

                # Run inference
                with torch.no_grad():
                    outputs = self.model(**inputs)

                # Post-process results
                detections = self.postprocess_outputs(outputs, image.shape)

            except Exception as e:
                logger.error(f"Detection failed: {e}")
                detections = self._simulate_detection(image)

        # Filter out edge artifacts and black shadows
        if self.filter_edge_artifacts and self.edge_filter is not None:
            original_count = len(detections)

            # Create valid area mask
            valid_mask = self.edge_filter.detect_edge_artifacts(image)

            # Filter detections
            detections = self.edge_filter.filter_detections(
                detections,
                image.shape,
                valid_mask
            )

            filtered_count = original_count - len(detections)
            if filtered_count > 0:
                logger.info(f"Filtered out {filtered_count} edge artifacts/black shadows")

        logger.info(f"Transformer detector found {len(detections)} valid particles")
        return detections
    
    def _simulate_detection(self, image: np.ndarray) -> List[Dict]:
        """Simulate particle detection for testing purposes"""
        import random
        
        height, width = image.shape[:2]
        num_particles = random.randint(5, 25)
        
        detections = []
        for i in range(num_particles):
            # Random bounding box
            x1 = random.randint(0, width - 50)
            y1 = random.randint(0, height - 50)
            x2 = x1 + random.randint(20, 100)
            y2 = y1 + random.randint(20, 100)
            
            # Ensure box is within image
            x2 = min(x2, width)
            y2 = min(y2, height)
            
            detection = {
                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                'confidence': random.uniform(0.6, 0.95),
                'class_id': random.randint(0, 4),
                'class_name': f"simulated_particle_{random.randint(0, 4)}",
                'center': [float((x1 + x2) / 2), float((y1 + y2) / 2)],
                'dimensions': {
                    'width_px': float(x2 - x1),
                    'height_px': float(y2 - y1),
                    'area_px': float((x2 - x1) * (y2 - y1))
                },
                'detection_method': 'simulation'
            }
            
            detections.append(detection)
        
        logger.info(f"Simulated {len(detections)} particle detections")
        return detections
    
    def benchmark_speed(self, image: np.ndarray, num_runs: int = 5) -> Dict:
        """
        Benchmark detection speed
        
        Args:
            image: Test image
            num_runs: Number of runs for averaging
            
        Returns:
            Speed benchmark results
        """
        import time
        
        if self.model is None:
            return {"error": "Model not loaded", "simulation_mode": True}
        
        times = []
        
        # Warm up
        _ = self.detect_particles(image)
        
        # Benchmark runs
        for _ in range(num_runs):
            start_time = time.time()
            _ = self.detect_particles(image)
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        return {
            "average_time_seconds": avg_time,
            "std_time_seconds": std_time,
            "fps": fps,
            "num_runs": num_runs,
            "model_name": self.model_name,
            "device": str(self.device)
        }


# Factory function for easy model creation
def create_transformer_detector(model_type: str = "detr", **kwargs) -> TransformerParticleDetector:
    """
    Factory function to create transformer detectors
    
    Args:
        model_type: Type of transformer model ('detr', 'dino', 'conditional', 'yolos')
        **kwargs: Additional arguments for detector
        
    Returns:
        Configured transformer detector
    """
    model_mapping = {
        "detr": "facebook/detr-resnet-50",
        "detr-101": "facebook/detr-resnet-101",
        "dino": "facebook/dino-detr-resnet-50", 
        "conditional": "microsoft/conditional-detr-resnet-50",
        "yolos-tiny": "hustvl/yolos-tiny",
        "yolos-small": "hustvl/yolos-small"
    }
    
    model_name = model_mapping.get(model_type.lower(), model_type)
    return TransformerParticleDetector(model_name=model_name, **kwargs)
