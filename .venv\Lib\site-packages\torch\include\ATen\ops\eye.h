#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/eye_ops.h>

namespace at {


// aten::eye(SymInt n, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye(int64_t n, at::TensorOptions options={}) {
    return at::_ops::eye::call(n, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor eye(int64_t n, at::TensorOptions options={}) {
    return at::_ops::eye::call(n, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
  }
}

// aten::eye(SymInt n, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye(int64_t n, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye::call(n, dtype, layout, device, pin_memory);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor eye(int64_t n, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye::call(n, dtype, layout, device, pin_memory);
  }
}

// aten::eye(SymInt n, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye_symint(c10::SymInt n, at::TensorOptions options={}) {
    return at::_ops::eye::call(n, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor eye(c10::SymInt n, at::TensorOptions options={}) {
    return at::_ops::eye::call(n, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
  }
}

// aten::eye(SymInt n, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye_symint(c10::SymInt n, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye::call(n, dtype, layout, device, pin_memory);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor eye(c10::SymInt n, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye::call(n, dtype, layout, device, pin_memory);
  }
}

// aten::eye.m(SymInt n, SymInt m, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye(int64_t n, int64_t m, at::TensorOptions options={}) {
    return at::_ops::eye_m::call(n, m, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor eye(int64_t n, int64_t m, at::TensorOptions options={}) {
    return at::_ops::eye_m::call(n, m, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
  }
}

// aten::eye.m(SymInt n, SymInt m, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye(int64_t n, int64_t m, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye_m::call(n, m, dtype, layout, device, pin_memory);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor eye(int64_t n, int64_t m, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye_m::call(n, m, dtype, layout, device, pin_memory);
  }
}

// aten::eye.m(SymInt n, SymInt m, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye_symint(c10::SymInt n, c10::SymInt m, at::TensorOptions options={}) {
    return at::_ops::eye_m::call(n, m, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor eye(c10::SymInt n, c10::SymInt m, at::TensorOptions options={}) {
    return at::_ops::eye_m::call(n, m, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
  }
}

// aten::eye.m(SymInt n, SymInt m, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor eye_symint(c10::SymInt n, c10::SymInt m, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye_m::call(n, m, dtype, layout, device, pin_memory);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor eye(c10::SymInt n, c10::SymInt m, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) {
    return at::_ops::eye_m::call(n, m, dtype, layout, device, pin_memory);
  }
}

// aten::eye.out(SymInt n, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_out(at::Tensor & out, int64_t n) {
    return at::_ops::eye_out::call(n, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & eye_out(at::Tensor & out, int64_t n) {
    return at::_ops::eye_out::call(n, out);
  }
}

// aten::eye.out(SymInt n, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_outf(int64_t n, at::Tensor & out) {
    return at::_ops::eye_out::call(n, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & eye_outf(int64_t n, at::Tensor & out) {
    return at::_ops::eye_out::call(n, out);
  }
}

// aten::eye.out(SymInt n, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_symint_out(at::Tensor & out, c10::SymInt n) {
    return at::_ops::eye_out::call(n, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & eye_out(at::Tensor & out, c10::SymInt n) {
    return at::_ops::eye_out::call(n, out);
  }
}

// aten::eye.out(SymInt n, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_symint_outf(c10::SymInt n, at::Tensor & out) {
    return at::_ops::eye_out::call(n, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & eye_outf(c10::SymInt n, at::Tensor & out) {
    return at::_ops::eye_out::call(n, out);
  }
}

// aten::eye.m_out(SymInt n, SymInt m, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_out(at::Tensor & out, int64_t n, int64_t m) {
    return at::_ops::eye_m_out::call(n, m, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & eye_out(at::Tensor & out, int64_t n, int64_t m) {
    return at::_ops::eye_m_out::call(n, m, out);
  }
}

// aten::eye.m_out(SymInt n, SymInt m, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_outf(int64_t n, int64_t m, at::Tensor & out) {
    return at::_ops::eye_m_out::call(n, m, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & eye_outf(int64_t n, int64_t m, at::Tensor & out) {
    return at::_ops::eye_m_out::call(n, m, out);
  }
}

// aten::eye.m_out(SymInt n, SymInt m, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_symint_out(at::Tensor & out, c10::SymInt n, c10::SymInt m) {
    return at::_ops::eye_m_out::call(n, m, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & eye_out(at::Tensor & out, c10::SymInt n, c10::SymInt m) {
    return at::_ops::eye_m_out::call(n, m, out);
  }
}

// aten::eye.m_out(SymInt n, SymInt m, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & eye_symint_outf(c10::SymInt n, c10::SymInt m, at::Tensor & out) {
    return at::_ops::eye_m_out::call(n, m, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & eye_outf(c10::SymInt n, c10::SymInt m, at::Tensor & out) {
    return at::_ops::eye_m_out::call(n, m, out);
  }
}

}
