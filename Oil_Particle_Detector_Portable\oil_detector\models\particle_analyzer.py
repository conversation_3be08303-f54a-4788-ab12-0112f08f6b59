"""
Advanced Particle Analysis Module for RT-DETR Detections
Combines RT-DETR detection with CNN wear classification and morphological analysis
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
import cv2
import numpy as np
from PIL import Image
import logging
from typing import List, Dict, Tuple, Optional
import os
import sys
from skimage import measure, morphology, filters
from skimage.feature import graycomatrix, graycoprops
import timm

# Add utils to path for edge artifact filter
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))
from edge_artifact_filter import EdgeArtifactFilter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ParticleAnalyzer:
    """Advanced particle analysis combining RT-DETR detection with morphological analysis"""
    
    def __init__(self, cnn_model_path: Optional[str] = None, filter_edge_artifacts: bool = True):
        """
        Initialize particle analyzer

        Args:
            cnn_model_path: Path to CNN wear classification model
            filter_edge_artifacts: Whether to filter out edge artifacts and black shadows
        """
        self.cnn_model_path = cnn_model_path
        self.cnn_model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.wear_classes = ["Early Wear", "Middle Wear", "Late Wear"]
        self.filter_edge_artifacts = filter_edge_artifacts

        # Edge artifact filter for removing black edges/shadows
        if self.filter_edge_artifacts:
            self.edge_filter = EdgeArtifactFilter(
                edge_threshold=30,      # Detect dark edges
                min_brightness=40,      # Minimum brightness for valid particles
                edge_margin=25,         # Exclude edge margins
                vignetting_detection=True  # Detect lens vignetting
            )
        else:
            self.edge_filter = None

        self.load_cnn_model()
        
    def load_cnn_model(self):
        """Load CNN model for wear classification"""
        try:
            if self.cnn_model_path and os.path.exists(self.cnn_model_path):
                logger.info(f"Loading CNN wear classification model from {self.cnn_model_path}")

                # Create ResNet50d model
                self.cnn_model = timm.create_model('resnet50d', num_classes=3, pretrained=False)

                # Load weights
                checkpoint = torch.load(self.cnn_model_path, map_location=self.device)

                # Handle different checkpoint formats
                if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint

                # Fix state dict keys - remove "model." prefix if present
                if any(key.startswith('model.') for key in state_dict.keys()):
                    logger.info("Fixing state dict keys by removing 'model.' prefix")
                    new_state_dict = {}
                    for key, value in state_dict.items():
                        if key.startswith('model.'):
                            new_key = key[6:]  # Remove 'model.' prefix
                            new_state_dict[new_key] = value
                        else:
                            new_state_dict[key] = value
                    state_dict = new_state_dict

                # Load the corrected state dict
                self.cnn_model.load_state_dict(state_dict, strict=False)

                self.cnn_model.to(self.device)
                self.cnn_model.eval()

                logger.info("CNN wear classification model loaded successfully")
            else:
                logger.warning("CNN model path not found, using simulation mode")
                self.cnn_model = None

        except Exception as e:
            logger.error(f"Failed to load CNN model: {e}")
            self.cnn_model = None
            
    def analyze_particles(self, image: np.ndarray, detections: List[Dict]) -> Dict:
        """
        Comprehensive particle analysis

        Args:
            image: Original image
            detections: RT-DETR detection results

        Returns:
            Complete analysis results
        """
        # Filter out edge artifacts and black shadows
        if self.filter_edge_artifacts and self.edge_filter is not None:
            original_count = len(detections)

            # Create valid area mask
            valid_mask = self.edge_filter.detect_edge_artifacts(image)

            # Filter detections
            detections = self.edge_filter.filter_detections(
                detections,
                image.shape,
                valid_mask
            )

            filtered_count = original_count - len(detections)
            if filtered_count > 0:
                logger.info(f"ParticleAnalyzer filtered out {filtered_count} edge artifacts/black shadows")

        analysis_results = {
            'total_particles': len(detections),
            'particle_details': [],
            'morphological_analysis': {},
            'wear_classification': {},
            'statistical_summary': {},
            'particle_distribution': {}
        }

        if len(detections) == 0:
            return analysis_results
            
        # Analyze each detected particle
        for i, detection in enumerate(detections):
            particle_analysis = self._analyze_single_particle(image, detection, i)
            analysis_results['particle_details'].append(particle_analysis)
            
        # Perform overall analysis
        analysis_results['morphological_analysis'] = self._morphological_analysis(
            image, analysis_results['particle_details']
        )
        
        analysis_results['wear_classification'] = self._classify_wear_stage(
            image, analysis_results['particle_details']
        )
        
        analysis_results['statistical_summary'] = self._statistical_analysis(
            analysis_results['particle_details']
        )
        
        analysis_results['particle_distribution'] = self._analyze_particle_distribution(
            analysis_results['particle_details']
        )
        
        return analysis_results
        
    def _analyze_single_particle(self, image: np.ndarray, detection: Dict, particle_id: int) -> Dict:
        """
        Analyze individual particle properties
        
        Args:
            image: Original image
            detection: Single particle detection
            particle_id: Unique particle identifier
            
        Returns:
            Detailed particle analysis
        """
        x1, y1, x2, y2 = detection['bbox']
        
        # Extract particle region
        particle_roi = image[y1:y2, x1:x2]
        
        if particle_roi.size == 0:
            return self._empty_particle_analysis(particle_id, detection)
            
        # Convert to grayscale for morphological analysis
        if len(particle_roi.shape) == 3:
            gray_roi = cv2.cvtColor(particle_roi, cv2.COLOR_BGR2GRAY)
        else:
            gray_roi = particle_roi
            
        # Morphological measurements
        morphology_data = self._extract_morphological_features(gray_roi)
        
        # Texture analysis
        texture_data = self._extract_texture_features(gray_roi)
        
        # Geometric analysis
        geometric_data = self._extract_geometric_features(detection['bbox'])
        
        particle_analysis = {
            'id': particle_id + 1,
            'detection': detection,
            'morphology': morphology_data,
            'texture': texture_data,
            'geometry': geometric_data,
            'classification': self._classify_particle_type(morphology_data, texture_data),
            'size_category': self._categorize_particle_size(geometric_data['area'])
        }
        
        return particle_analysis
        
    def _extract_morphological_features(self, gray_roi: np.ndarray) -> Dict:
        """Extract morphological features from particle ROI"""
        try:
            # Threshold the image
            _, binary = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return self._default_morphology()
                
            # Get largest contour (main particle)
            main_contour = max(contours, key=cv2.contourArea)
            
            # Calculate morphological properties
            area = cv2.contourArea(main_contour)
            perimeter = cv2.arcLength(main_contour, True)
            
            # Fit ellipse if possible
            if len(main_contour) >= 5:
                ellipse = cv2.fitEllipse(main_contour)
                major_axis = max(ellipse[1])
                minor_axis = min(ellipse[1])
                aspect_ratio = major_axis / minor_axis if minor_axis > 0 else 1.0
                orientation = ellipse[2]
            else:
                aspect_ratio = 1.0
                orientation = 0.0
                major_axis = minor_axis = np.sqrt(area / np.pi) * 2
                
            # Circularity
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            
            # Solidity
            hull = cv2.convexHull(main_contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            
            return {
                'area': float(area),
                'perimeter': float(perimeter),
                'major_axis': float(major_axis),
                'minor_axis': float(minor_axis),
                'aspect_ratio': float(aspect_ratio),
                'circularity': float(circularity),
                'solidity': float(solidity),
                'orientation': float(orientation)
            }
            
        except Exception as e:
            logger.warning(f"Morphological analysis failed: {e}")
            return self._default_morphology()
            
    def _extract_texture_features(self, gray_roi: np.ndarray) -> Dict:
        """Extract texture features using GLCM"""
        try:
            if gray_roi.size < 4:  # Too small for texture analysis
                return self._default_texture()
                
            # Resize if too small
            if min(gray_roi.shape) < 8:
                gray_roi = cv2.resize(gray_roi, (16, 16))
                
            # Calculate GLCM
            glcm = graycomatrix(gray_roi, distances=[1], angles=[0], levels=256, symmetric=True, normed=True)
            
            # Extract texture properties
            contrast = graycoprops(glcm, 'contrast')[0, 0]
            dissimilarity = graycoprops(glcm, 'dissimilarity')[0, 0]
            homogeneity = graycoprops(glcm, 'homogeneity')[0, 0]
            energy = graycoprops(glcm, 'energy')[0, 0]
            correlation = graycoprops(glcm, 'correlation')[0, 0]
            
            return {
                'contrast': float(contrast),
                'dissimilarity': float(dissimilarity),
                'homogeneity': float(homogeneity),
                'energy': float(energy),
                'correlation': float(correlation)
            }
            
        except Exception as e:
            logger.warning(f"Texture analysis failed: {e}")
            return self._default_texture()
            
    def _extract_geometric_features(self, bbox: List[int]) -> Dict:
        """Extract geometric features from bounding box"""
        x1, y1, x2, y2 = bbox
        width = x2 - x1
        height = y2 - y1
        area = width * height
        
        return {
            'width': width,
            'height': height,
            'area': area,
            'center_x': (x1 + x2) // 2,
            'center_y': (y1 + y2) // 2,
            'bbox_aspect_ratio': width / height if height > 0 else 1.0
        }
        
    def _classify_particle_type(self, morphology: Dict, texture: Dict) -> str:
        """Classify particle type based on morphological and texture features"""
        aspect_ratio = morphology.get('aspect_ratio', 1.0)
        circularity = morphology.get('circularity', 0.0)
        solidity = morphology.get('solidity', 0.0)
        
        # Classification rules based on morphological characteristics
        if circularity > 0.8 and aspect_ratio < 1.3:
            return "Spherical Particles"
        elif aspect_ratio > 3.0 and solidity > 0.7:
            return "Cutting Particles"
        elif aspect_ratio > 2.0 and circularity < 0.5:
            return "Sliding Particles"
        elif circularity < 0.3 and solidity < 0.6:
            return "Lamellar Particles"
        else:
            return "Normal Particles"
            
    def _categorize_particle_size(self, area: float) -> str:
        """Categorize particle by size"""
        if area < 100:
            return "Small"
        elif area < 500:
            return "Medium"
        else:
            return "Large"
            
    def _morphological_analysis(self, image: np.ndarray, particle_details: List[Dict]) -> Dict:
        """Overall morphological analysis of all particles"""
        if not particle_details:
            return {}
            
        areas = [p['morphology']['area'] for p in particle_details]
        aspect_ratios = [p['morphology']['aspect_ratio'] for p in particle_details]
        circularities = [p['morphology']['circularity'] for p in particle_details]
        
        return {
            'mean_area': float(np.mean(areas)),
            'std_area': float(np.std(areas)),
            'mean_aspect_ratio': float(np.mean(aspect_ratios)),
            'mean_circularity': float(np.mean(circularities)),
            'size_distribution': {
                'small': len([a for a in areas if a < 100]),
                'medium': len([a for a in areas if 100 <= a < 500]),
                'large': len([a for a in areas if a >= 500])
            }
        }
        
    def _classify_wear_stage(self, image: np.ndarray, particle_details: List[Dict]) -> Dict:
        """Classify overall wear stage"""
        if self.cnn_model is not None:
            return self._cnn_wear_classification(image)
        else:
            return self._rule_based_wear_classification(particle_details)
            
    def _cnn_wear_classification(self, image: np.ndarray) -> Dict:
        """CNN-based wear classification"""
        try:
            # Preprocess image for CNN
            transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            
            # Convert BGR to RGB if needed
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
                
            input_tensor = transform(image_rgb).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.cnn_model(input_tensor)
                probabilities = torch.nn.functional.softmax(outputs, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
                confidence = probabilities[0][predicted_class].item()
                
            return {
                'wear_stage': self.wear_classes[predicted_class],
                'confidence': float(confidence),
                'probabilities': {
                    self.wear_classes[i]: float(probabilities[0][i])
                    for i in range(len(self.wear_classes))
                },
                'method': 'CNN'
            }
            
        except Exception as e:
            logger.error(f"CNN wear classification failed: {e}")
            return self._rule_based_wear_classification([])
            
    def _rule_based_wear_classification(self, particle_details: List[Dict]) -> Dict:
        """Rule-based wear classification fallback"""
        total_particles = len(particle_details)
        
        if total_particles == 0:
            wear_stage = "Early Wear"
            confidence = 0.5
        else:
            # Count different particle types
            cutting_count = len([p for p in particle_details if p['classification'] == 'Cutting Particles'])
            sliding_count = len([p for p in particle_details if p['classification'] == 'Sliding Particles'])
            
            # Simple rule-based classification
            if total_particles < 15:
                wear_stage = "Early Wear"
                confidence = 0.7
            elif total_particles < 30 or (cutting_count + sliding_count) < 5:
                wear_stage = "Middle Wear"
                confidence = 0.6
            else:
                wear_stage = "Late Wear"
                confidence = 0.8
                
        return {
            'wear_stage': wear_stage,
            'confidence': confidence,
            'method': 'Rule-based',
            'particle_count': total_particles
        }
        
    def _statistical_analysis(self, particle_details: List[Dict]) -> Dict:
        """Statistical analysis of particles"""
        if not particle_details:
            return {}
            
        areas = [p['morphology']['area'] for p in particle_details]
        lengths = [p['morphology']['major_axis'] for p in particle_details]
        
        return {
            'total_count': len(particle_details),
            'mean_area': float(np.mean(areas)),
            'std_area': float(np.std(areas)),
            'mean_length': float(np.mean(lengths)),
            'max_length': float(np.max(lengths)),
            'min_length': float(np.min(lengths)),
            'concentration': len(particle_details) * 2.5  # Simulated concentration
        }
        
    def _analyze_particle_distribution(self, particle_details: List[Dict]) -> Dict:
        """Analyze particle type distribution"""
        if not particle_details:
            return {}
            
        type_counts = {}
        for particle in particle_details:
            ptype = particle['classification']
            type_counts[ptype] = type_counts.get(ptype, 0) + 1
            
        total = len(particle_details)
        distribution = {
            ptype: {
                'count': count,
                'percentage': (count / total) * 100 if total > 0 else 0
            }
            for ptype, count in type_counts.items()
        }
        
        return distribution
        
    def _default_morphology(self) -> Dict:
        """Default morphology values"""
        return {
            'area': 0.0, 'perimeter': 0.0, 'major_axis': 0.0, 'minor_axis': 0.0,
            'aspect_ratio': 1.0, 'circularity': 0.0, 'solidity': 0.0, 'orientation': 0.0
        }
        
    def _default_texture(self) -> Dict:
        """Default texture values"""
        return {
            'contrast': 0.0, 'dissimilarity': 0.0, 'homogeneity': 0.0,
            'energy': 0.0, 'correlation': 0.0
        }
        
    def _empty_particle_analysis(self, particle_id: int, detection: Dict) -> Dict:
        """Empty particle analysis for invalid ROIs"""
        return {
            'id': particle_id + 1,
            'detection': detection,
            'morphology': self._default_morphology(),
            'texture': self._default_texture(),
            'geometry': {'width': 0, 'height': 0, 'area': 0, 'center_x': 0, 'center_y': 0, 'bbox_aspect_ratio': 1.0},
            'classification': 'Unknown',
            'size_category': 'Small'
        }
