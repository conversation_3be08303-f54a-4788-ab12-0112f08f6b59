"""
Comprehensive Evaluation Framework for Particle Detection
Supports multiple metrics including mAP, precision, recall, and particle-specific metrics
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import logging
from sklearn.metrics import precision_recall_curve, average_precision_score
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


class ParticleDetectionEvaluator:
    """
    Comprehensive evaluator for particle detection models
    Calculates mAP, precision, recall, and particle-specific metrics
    """
    
    def __init__(self, 
                 iou_thresholds: List[float] = [0.5, 0.75, 0.9],
                 confidence_threshold: float = 0.5,
                 class_names: Optional[List[str]] = None):
        """
        Args:
            iou_thresholds: IoU thresholds for evaluation
            confidence_threshold: Minimum confidence for detections
            class_names: Names of particle classes
        """
        self.iou_thresholds = iou_thresholds
        self.confidence_threshold = confidence_threshold
        self.class_names = class_names or [
            'background', 'spherical', 'irregular', 'elongated', 'cluster', 'micro'
        ]
        
        # Storage for predictions and ground truth
        self.predictions = []
        self.ground_truths = []
        
        # Metrics storage
        self.metrics = {}
        
    def add_batch(self, predictions: List[Dict], ground_truths: List[Dict]):
        """
        Add a batch of predictions and ground truths
        
        Args:
            predictions: List of prediction dicts with 'boxes', 'scores', 'labels'
            ground_truths: List of ground truth dicts with 'boxes', 'labels'
        """
        self.predictions.extend(predictions)
        self.ground_truths.extend(ground_truths)
    
    def compute_metrics(self) -> Dict[str, float]:
        """Compute all evaluation metrics"""
        logger.info("Computing evaluation metrics...")
        
        # Compute mAP at different IoU thresholds
        for iou_thresh in self.iou_thresholds:
            map_score = self._compute_map(iou_thresh)
            self.metrics[f'mAP@{iou_thresh}'] = map_score
        
        # Compute average mAP (COCO style)
        iou_range = np.arange(0.5, 1.0, 0.05)
        map_scores = [self._compute_map(iou) for iou in iou_range]
        self.metrics['mAP@0.5:0.95'] = np.mean(map_scores)
        
        # Compute per-class metrics
        class_metrics = self._compute_class_metrics()
        self.metrics.update(class_metrics)
        
        # Compute size-specific metrics
        size_metrics = self._compute_size_metrics()
        self.metrics.update(size_metrics)
        
        # Compute detection statistics
        detection_stats = self._compute_detection_stats()
        self.metrics.update(detection_stats)
        
        logger.info(f"Evaluation completed. mAP@0.5: {self.metrics.get('mAP@0.5', 0):.4f}")
        
        return self.metrics
    
    def _compute_map(self, iou_threshold: float) -> float:
        """Compute mean Average Precision at given IoU threshold"""
        ap_scores = []
        
        for class_id in range(1, len(self.class_names)):  # Skip background
            ap = self._compute_ap_for_class(class_id, iou_threshold)
            ap_scores.append(ap)
        
        return np.mean(ap_scores) if ap_scores else 0.0
    
    def _compute_ap_for_class(self, class_id: int, iou_threshold: float) -> float:
        """Compute Average Precision for a specific class"""
        # Collect all predictions and ground truths for this class
        class_predictions = []
        class_ground_truths = []
        
        for i, (pred, gt) in enumerate(zip(self.predictions, self.ground_truths)):
            # Filter predictions for this class
            if 'labels' in pred and len(pred['labels']) > 0:
                class_mask = pred['labels'] == class_id
                if class_mask.any():
                    class_pred = {
                        'boxes': pred['boxes'][class_mask],
                        'scores': pred['scores'][class_mask],
                        'image_id': i
                    }
                    class_predictions.append(class_pred)
            
            # Filter ground truths for this class
            if 'labels' in gt and len(gt['labels']) > 0:
                gt_class_mask = gt['labels'] == class_id
                if gt_class_mask.any():
                    class_gt = {
                        'boxes': gt['boxes'][gt_class_mask],
                        'image_id': i
                    }
                    class_ground_truths.append(class_gt)
        
        if not class_predictions or not class_ground_truths:
            return 0.0
        
        # Sort predictions by confidence
        all_predictions = []
        for pred in class_predictions:
            for j in range(len(pred['boxes'])):
                all_predictions.append({
                    'box': pred['boxes'][j],
                    'score': pred['scores'][j],
                    'image_id': pred['image_id']
                })
        
        all_predictions.sort(key=lambda x: x['score'], reverse=True)
        
        # Calculate precision and recall
        tp = np.zeros(len(all_predictions))
        fp = np.zeros(len(all_predictions))
        
        # Count total ground truth boxes
        total_gt = sum(len(gt['boxes']) for gt in class_ground_truths)
        
        # Track which ground truth boxes have been matched
        gt_matched = defaultdict(set)
        
        for i, pred in enumerate(all_predictions):
            # Find ground truth for this image
            image_gt = None
            for gt in class_ground_truths:
                if gt['image_id'] == pred['image_id']:
                    image_gt = gt
                    break
            
            if image_gt is None:
                fp[i] = 1
                continue
            
            # Calculate IoU with all ground truth boxes
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt_box in enumerate(image_gt['boxes']):
                iou = self._calculate_iou(pred['box'], gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            # Check if this is a true positive
            if best_iou >= iou_threshold:
                gt_key = (pred['image_id'], best_gt_idx)
                if gt_key not in gt_matched[pred['image_id']]:
                    tp[i] = 1
                    gt_matched[pred['image_id']].add(gt_key)
                else:
                    fp[i] = 1  # Already matched
            else:
                fp[i] = 1
        
        # Calculate cumulative precision and recall
        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)
        
        recalls = tp_cumsum / max(total_gt, 1)
        precisions = tp_cumsum / (tp_cumsum + fp_cumsum + 1e-8)
        
        # Calculate AP using 11-point interpolation
        ap = self._calculate_ap_11_point(precisions, recalls)
        
        return ap
    
    def _calculate_iou(self, box1: torch.Tensor, box2: torch.Tensor) -> float:
        """Calculate IoU between two boxes"""
        # Convert to numpy if needed
        if isinstance(box1, torch.Tensor):
            box1 = box1.cpu().numpy()
        if isinstance(box2, torch.Tensor):
            box2 = box2.cpu().numpy()
        
        # Calculate intersection
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # Calculate union
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_ap_11_point(self, precisions: np.ndarray, recalls: np.ndarray) -> float:
        """Calculate AP using 11-point interpolation"""
        ap = 0.0
        for t in np.arange(0, 1.1, 0.1):
            if np.sum(recalls >= t) == 0:
                p = 0
            else:
                p = np.max(precisions[recalls >= t])
            ap += p / 11.0
        return ap
    
    def _compute_class_metrics(self) -> Dict[str, float]:
        """Compute per-class precision, recall, and F1 scores"""
        metrics = {}
        
        for class_id in range(1, len(self.class_names)):
            class_name = self.class_names[class_id]
            
            # Calculate precision, recall, F1 for this class
            precision, recall, f1 = self._compute_class_prf(class_id)
            
            metrics[f'{class_name}_precision'] = precision
            metrics[f'{class_name}_recall'] = recall
            metrics[f'{class_name}_f1'] = f1
        
        return metrics
    
    def _compute_class_prf(self, class_id: int) -> Tuple[float, float, float]:
        """Compute precision, recall, F1 for a specific class"""
        tp = fp = fn = 0
        
        for pred, gt in zip(self.predictions, self.ground_truths):
            # Count predictions for this class
            if 'labels' in pred and len(pred['labels']) > 0:
                pred_mask = (pred['labels'] == class_id) & (pred['scores'] >= self.confidence_threshold)
                pred_count = pred_mask.sum().item()
            else:
                pred_count = 0
            
            # Count ground truths for this class
            if 'labels' in gt and len(gt['labels']) > 0:
                gt_count = (gt['labels'] == class_id).sum().item()
            else:
                gt_count = 0
            
            # Simple counting (this is simplified - real implementation would need IoU matching)
            tp += min(pred_count, gt_count)
            fp += max(0, pred_count - gt_count)
            fn += max(0, gt_count - pred_count)
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return precision, recall, f1
    
    def _compute_size_metrics(self) -> Dict[str, float]:
        """Compute metrics for different object sizes"""
        size_metrics = {}
        
        # Define size categories (in pixels)
        size_categories = {
            'small': (0, 32**2),      # < 32x32 pixels
            'medium': (32**2, 96**2), # 32x32 to 96x96 pixels
            'large': (96**2, float('inf'))  # > 96x96 pixels
        }
        
        for size_name, (min_area, max_area) in size_categories.items():
            ap = self._compute_size_specific_ap(min_area, max_area)
            size_metrics[f'{size_name}_object_ap'] = ap
        
        return size_metrics
    
    def _compute_size_specific_ap(self, min_area: float, max_area: float) -> float:
        """Compute AP for objects in specific size range"""
        # This is a simplified implementation
        # Real implementation would filter objects by size and compute AP
        return 0.0  # Placeholder
    
    def _compute_detection_stats(self) -> Dict[str, float]:
        """Compute detection statistics"""
        total_predictions = sum(len(pred.get('boxes', [])) for pred in self.predictions)
        total_ground_truths = sum(len(gt.get('boxes', [])) for gt in self.ground_truths)
        
        return {
            'total_predictions': total_predictions,
            'total_ground_truths': total_ground_truths,
            'avg_predictions_per_image': total_predictions / len(self.predictions) if self.predictions else 0,
            'avg_ground_truths_per_image': total_ground_truths / len(self.ground_truths) if self.ground_truths else 0
        }
    
    def generate_report(self) -> str:
        """Generate a comprehensive evaluation report"""
        if not self.metrics:
            self.compute_metrics()
        
        report = "=== Particle Detection Evaluation Report ===\n\n"
        
        # Main metrics
        report += "Main Metrics:\n"
        report += f"mAP@0.5: {self.metrics.get('mAP@0.5', 0):.4f}\n"
        report += f"mAP@0.75: {self.metrics.get('mAP@0.75', 0):.4f}\n"
        report += f"mAP@0.5:0.95: {self.metrics.get('mAP@0.5:0.95', 0):.4f}\n\n"
        
        # Per-class metrics
        report += "Per-Class Metrics:\n"
        for class_name in self.class_names[1:]:  # Skip background
            precision = self.metrics.get(f'{class_name}_precision', 0)
            recall = self.metrics.get(f'{class_name}_recall', 0)
            f1 = self.metrics.get(f'{class_name}_f1', 0)
            report += f"{class_name}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}\n"
        
        report += "\n"
        
        # Size-specific metrics
        report += "Size-Specific Metrics:\n"
        for size in ['small', 'medium', 'large']:
            ap = self.metrics.get(f'{size}_object_ap', 0)
            report += f"{size.capitalize()} objects AP: {ap:.4f}\n"
        
        return report

    def evaluate_model_with_tta(self, model, dataloader, use_tta: bool = False) -> Dict[str, float]:
        """
        Evaluate model on dataset with optional test-time augmentation

        Args:
            model: Trained model
            dataloader: DataLoader for evaluation
            use_tta: Whether to use test-time augmentation

        Returns:
            Dictionary with evaluation metrics
        """
        from tqdm import tqdm

        model.eval()
        self.reset()  # Clear previous results

        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Evaluating"):
                images = batch['images']
                targets = batch['targets']

                if use_tta:
                    # Test-time augmentation
                    predictions = self._predict_with_tta(model, images)
                else:
                    # Standard prediction
                    outputs = model(images)
                    predictions = self._process_model_outputs(outputs)

                # Convert targets to expected format
                ground_truths = self._process_targets(targets)

                # Add to evaluator
                self.add_batch(predictions, ground_truths)

        # Calculate comprehensive metrics
        metrics = self.compute_metrics()

        return metrics

    def _predict_with_tta(self, model, images):
        """Predict with test-time augmentation"""
        try:
            from augmentations.particle_augmentations import get_test_time_augmentations
        except ImportError:
            logger.warning("TTA augmentations not available, using standard prediction")
            outputs = model(images)
            return self._process_model_outputs(outputs)

        tta_transforms = get_test_time_augmentations()
        all_tta_predictions = []

        for transform in tta_transforms:
            # Apply augmentation to each image in batch
            augmented_images = []
            for img in images:
                # Convert tensor to numpy for augmentation
                if isinstance(img, torch.Tensor):
                    img_np = img.permute(1, 2, 0).cpu().numpy()
                    img_np = (img_np * 255).astype(np.uint8)
                else:
                    img_np = img

                # Apply transform
                try:
                    augmented = transform(image=img_np)
                    aug_img = augmented['image']
                    augmented_images.append(aug_img)
                except Exception as e:
                    logger.warning(f"TTA transform failed: {e}")
                    # Fall back to original image
                    augmented_images.append(img)

            # Stack augmented images
            try:
                aug_batch = torch.stack(augmented_images).to(images.device)

                # Get predictions
                outputs = model(aug_batch)
                predictions = self._process_model_outputs(outputs)
                all_tta_predictions.append(predictions)
            except Exception as e:
                logger.warning(f"TTA prediction failed: {e}")
                continue

        # Average TTA predictions
        if all_tta_predictions:
            return self._average_tta_predictions(all_tta_predictions)
        else:
            # Fall back to standard prediction
            outputs = model(images)
            return self._process_model_outputs(outputs)

    def _process_model_outputs(self, outputs):
        """Process model outputs into standard format"""
        predictions = []

        # Handle different model output formats
        if hasattr(outputs, 'logits') and hasattr(outputs, 'pred_boxes'):
            # DETR-style outputs
            logits = outputs.logits
            boxes = outputs.pred_boxes

            for i in range(logits.shape[0]):  # Batch size
                # Get probabilities
                probs = torch.softmax(logits[i], dim=-1)
                max_probs, predicted_classes = torch.max(probs[:, :-1], dim=-1)  # Exclude background

                # Filter by confidence
                keep = max_probs > self.confidence_threshold

                pred = {
                    'boxes': boxes[i][keep].cpu().numpy(),
                    'scores': max_probs[keep].cpu().numpy(),
                    'labels': predicted_classes[keep].cpu().numpy()
                }
                predictions.append(pred)
        else:
            # Handle other output formats
            logger.warning("Unknown model output format, returning empty predictions")
            predictions = [{'boxes': np.array([]), 'scores': np.array([]), 'labels': np.array([])}]

        return predictions

    def _process_targets(self, targets):
        """Process targets into standard format"""
        ground_truths = []

        for target in targets:
            if isinstance(target, dict):
                gt = {
                    'boxes': target.get('boxes', torch.tensor([])).cpu().numpy(),
                    'labels': target.get('labels', torch.tensor([])).cpu().numpy()
                }
            else:
                # Handle other target formats
                gt = {'boxes': np.array([]), 'labels': np.array([])}

            ground_truths.append(gt)

        return ground_truths

    def _average_tta_predictions(self, tta_predictions):
        """Average predictions from test-time augmentation"""
        if not tta_predictions or len(tta_predictions) == 0:
            return []

        # Simple averaging - in practice you might want to reverse augmentations
        # For now, return the first set of predictions
        # Full TTA implementation would properly average/ensemble the results
        return tta_predictions[0]

    def reset(self):
        """Reset evaluator state"""
        self.predictions = []
        self.ground_truths = []
        self.metrics = {}

    def evaluate_ensemble(self, ensemble_model, dataloader) -> Dict[str, float]:
        """
        Evaluate ensemble model

        Args:
            ensemble_model: Ensemble model instance
            dataloader: DataLoader for evaluation

        Returns:
            Dictionary with evaluation metrics
        """
        from tqdm import tqdm

        self.reset()

        for batch in tqdm(dataloader, desc="Evaluating Ensemble"):
            images = batch['images']
            targets = batch['targets']

            # Get ensemble predictions
            predictions = []
            for img in images:
                # Convert tensor to numpy
                if isinstance(img, torch.Tensor):
                    img_np = img.permute(1, 2, 0).cpu().numpy()
                    img_np = (img_np * 255).astype(np.uint8)
                else:
                    img_np = img

                # Get ensemble prediction
                pred = ensemble_model.predict(img_np, use_tta=True)

                # Convert to expected format
                pred_dict = {
                    'boxes': pred['boxes'].cpu().numpy() if isinstance(pred['boxes'], torch.Tensor) else pred['boxes'],
                    'scores': pred['scores'].cpu().numpy() if isinstance(pred['scores'], torch.Tensor) else pred['scores'],
                    'labels': pred['labels'].cpu().numpy() if isinstance(pred['labels'], torch.Tensor) else pred['labels']
                }
                predictions.append(pred_dict)

            # Process targets
            ground_truths = self._process_targets(targets)

            # Add to evaluator
            self.add_batch(predictions, ground_truths)

        # Calculate metrics
        metrics = self.compute_metrics()

        return metrics
    
    def reset(self):
        """Reset evaluator state"""
        self.predictions.clear()
        self.ground_truths.clear()
        self.metrics.clear()
