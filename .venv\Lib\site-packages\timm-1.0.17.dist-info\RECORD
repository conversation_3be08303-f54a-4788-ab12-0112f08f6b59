timm-1.0.17.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
timm-1.0.17.dist-info/METADATA,sha256=gc1MzhVgQhgclEc7Pdx5cHkmnG9aBO9yqC38R0eC06c,59873
timm-1.0.17.dist-info/RECORD,,
timm-1.0.17.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
timm-1.0.17.dist-info/WHEEL,sha256=9P2ygRxDrTJz3gsagc0Z96ukrxjr-LFBGOgv3AuKlCA,90
timm-1.0.17.dist-info/entry_points.txt,sha256=6OYgBcLyFCUgeqLgnvMyOJxPCWzgy7se4rLPKtNonMs,34
timm-1.0.17.dist-info/licenses/LICENSE,sha256=cbERYg-jLBeoDM1tstp1nTGlkeSX2LXzghdPWdG1nUk,11343
timm/__init__.py,sha256=lmjfj_SLM_hGQozXYHOZzxS08P7vFXkw6r9t7-kysdM,604
timm/__pycache__/__init__.cpython-312.pyc,,
timm/__pycache__/version.cpython-312.pyc,,
timm/data/__init__.py,sha256=mHBh5_xKp2crz2tNx9y72n452RcHJW48LdT_dCPvQVA,1241
timm/data/__pycache__/__init__.cpython-312.pyc,,
timm/data/__pycache__/auto_augment.cpython-312.pyc,,
timm/data/__pycache__/config.cpython-312.pyc,,
timm/data/__pycache__/constants.cpython-312.pyc,,
timm/data/__pycache__/dataset.cpython-312.pyc,,
timm/data/__pycache__/dataset_factory.cpython-312.pyc,,
timm/data/__pycache__/dataset_info.cpython-312.pyc,,
timm/data/__pycache__/distributed_sampler.cpython-312.pyc,,
timm/data/__pycache__/imagenet_info.cpython-312.pyc,,
timm/data/__pycache__/loader.cpython-312.pyc,,
timm/data/__pycache__/mixup.cpython-312.pyc,,
timm/data/__pycache__/naflex_dataset.cpython-312.pyc,,
timm/data/__pycache__/naflex_loader.cpython-312.pyc,,
timm/data/__pycache__/naflex_mixup.cpython-312.pyc,,
timm/data/__pycache__/naflex_random_erasing.cpython-312.pyc,,
timm/data/__pycache__/naflex_transforms.cpython-312.pyc,,
timm/data/__pycache__/random_erasing.cpython-312.pyc,,
timm/data/__pycache__/real_labels.cpython-312.pyc,,
timm/data/__pycache__/tf_preprocessing.cpython-312.pyc,,
timm/data/__pycache__/transforms.cpython-312.pyc,,
timm/data/__pycache__/transforms_factory.cpython-312.pyc,,
timm/data/_info/imagenet12k_synsets.txt,sha256=9kg-efGKm2cNQ9kV3bpAu8r4LPxOkqB71EBXudRJNfM,118210
timm/data/_info/imagenet21k_goog_synsets.txt,sha256=ZjYr3t822TM4LtylST_FYtzBcSjONkA8nnMKdfSMsvI,218430
timm/data/_info/imagenet21k_goog_to_12k_indices.txt,sha256=a1iOnVXH_tZ6rILpG-6r5-rOLeHtZxFQEMoHYXnoxQg,64070
timm/data/_info/imagenet21k_goog_to_22k_indices.txt,sha256=hqdngDZvgHqfgEYX6iRMob9YF0FNAzDPEbop4oXBzFc,119937
timm/data/_info/imagenet21k_miil_synsets.txt,sha256=zXbZd9qqLxOkqR_KD1NxPy8pZ1MI1M4PDuDlsHoxZMY,112210
timm/data/_info/imagenet21k_miil_w21_synsets.txt,sha256=9J3ePWmUa4Y1WGQy8081mF-AfRaChs5f3nRqrDp9lMg,104500
timm/data/_info/imagenet22k_ms_synsets.txt,sha256=8sesR1AOHR1wQy08suKyGHxogBJUOYMr9LQDj3Fsong,218420
timm/data/_info/imagenet22k_ms_to_12k_indices.txt,sha256=wMkQkjtw2JKm-KzN6bDKvaqN1nuMD8_QuC1IFVei1js,63625
timm/data/_info/imagenet22k_ms_to_22k_indices.txt,sha256=Sp_dB0M-ncOF_j_rAhWGMI-JSj3LhfSIKyXtIEz2MyE,119938
timm/data/_info/imagenet22k_synsets.txt,sha256=CuJFOHUZc3l7KdjexBKyvcYbxOw-zQgeugc1gEI2u9I,218410
timm/data/_info/imagenet22k_to_12k_indices.txt,sha256=0_-qtIBkurHqfOFibHL1oBHuRQPKxhK5DuHaDRR8joc,64070
timm/data/_info/imagenet_a_indices.txt,sha256=6BIjo1rdbnj5r-byFWUsGSbr7-HY78JsbpI9eskUOTc,774
timm/data/_info/imagenet_a_synsets.txt,sha256=mCaiQWbnTOYvuHsniJh0yiaRdULHRXEjCdbiWFXWO8I,2000
timm/data/_info/imagenet_r_indices.txt,sha256=keCdL_CgAhBLwNN6iSp-df_CzA20eytQYEejSTuRFtI,769
timm/data/_info/imagenet_r_synsets.txt,sha256=pqByn3qZIwKAY5yrblHgFIWjtBpnbqNWIaUBydboPKQ,2000
timm/data/_info/imagenet_real_labels.json,sha256=2D6b_zdMYxquhDnrBkxwGazFbhs7w_Vrg4DCpxCwIgs,388478
timm/data/_info/imagenet_synset_to_definition.txt,sha256=GR4ifUj8jHt-LSo8rEH8iy61F9nSgZ0O28kcV2clDu0,1748917
timm/data/_info/imagenet_synset_to_lemma.txt,sha256=G4ur2hh0IaS94MnFoZfDb2vdqWL3yhH_soE4BsuyF48,741457
timm/data/_info/imagenet_synsets.txt,sha256=cAArD_XeYKOheoLb_P8pGTH5YiXd-UGtLhgvw54YPRU,10000
timm/data/_info/mini_imagenet_indices.txt,sha256=AGR2p4Qxvbev7KvoFgl4cV-h88uQO7uGp72jkaqNJ1I,393
timm/data/_info/mini_imagenet_synsets.txt,sha256=Ab6JqPac478fNh1wHIS6XrO3OZP9xW8wZBJWED3EXlc,1000
timm/data/auto_augment.py,sha256=_QpwfBfj2SW75yBEDmZr8qrFFsqOAbZDF1sxfxvTrHI,35599
timm/data/config.py,sha256=uf2p-mNW_wntCe34K_Xr3J45cJh8IWEl_cMwgyKGR-g,4616
timm/data/constants.py,sha256=ZkSr7QArcsDyOOBRYQpLe4c2VOJKRAYoDRU1JwYJseE,442
timm/data/dataset.py,sha256=hukTF1VlIIa3GhDRa-eM1MgEfQaHA-wJ189oZuLzpuY,6307
timm/data/dataset_factory.py,sha256=EFjMuczTksO76fluXn0OXmU7Wkh951gCNaqG-O8_5Ag,8627
timm/data/dataset_info.py,sha256=fAjTkbi-3oEZveKg-n1cxOYM_gcD5kcUeWMWWRRkhOQ,2391
timm/data/distributed_sampler.py,sha256=mfcS_bzL_zEIREpRer8LAdXw1WvUFYJrXgqKw2fqlHE,5540
timm/data/imagenet_info.py,sha256=zdUh7jlwK0tPzJXalTCEiMBTp_aGh_AvJ7NpwCGMMqU,4167
timm/data/loader.py,sha256=3o5HPJ3rN1H4cvQjvWUO_Z2HQkukOEpRt2Vpj4lU9vc,16268
timm/data/mixup.py,sha256=U72B703T2AwXlf1CXG6r0n5XLaSUHgRAkCwrF-9M48s,16078
timm/data/naflex_dataset.py,sha256=TuBhSKrSIY0qTpTFD_Au4GOJlUjy4uRTGgKCl04BO2s,24473
timm/data/naflex_loader.py,sha256=fEeGelH_Y-21TW9Si5oiiQL21MP0fUPgJWdiIwAHCeI,15703
timm/data/naflex_mixup.py,sha256=y_Xjq6KfQuEWTeu1-_obNtW4478kbrTxrrBjFF957zk,9001
timm/data/naflex_random_erasing.py,sha256=HlgqI57eRGIub5_4XkEdQWgpQho62O0fa7caO9xiw3k,14283
timm/data/naflex_transforms.py,sha256=zuq8qrp_thgIstVlFHh88e4peHK2KEG6vwnnGP5ocMA,32535
timm/data/random_erasing.py,sha256=jSTJq-1zGZT1zRWv5ByyBuA4lQdj_EFcoibdLjRQaDo,4964
timm/data/readers/__init__.py,sha256=AuKgQjZ5q9XILIZu6iRDIJy-Xydig_zC1TlaVRmRcCY,72
timm/data/readers/__pycache__/__init__.cpython-312.pyc,,
timm/data/readers/__pycache__/class_map.cpython-312.pyc,,
timm/data/readers/__pycache__/img_extensions.cpython-312.pyc,,
timm/data/readers/__pycache__/reader.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_factory.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_hfds.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_hfids.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_image_folder.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_image_in_tar.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_image_tar.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_tfds.cpython-312.pyc,,
timm/data/readers/__pycache__/reader_wds.cpython-312.pyc,,
timm/data/readers/__pycache__/shared_count.cpython-312.pyc,,
timm/data/readers/class_map.py,sha256=dMXs4PuEDqJroaOmlBttTpjMlaeXMiB4H4barDKtzvc,895
timm/data/readers/img_extensions.py,sha256=KTtkCyfrOhLwH7vws7k7xshkEqekdrEGZ-rmErySvcQ,1482
timm/data/readers/reader.py,sha256=_Fp97wLI1cxFFI1QlOkvWIKwdmtQaPEgH97KmtQDZoI,487
timm/data/readers/reader_factory.py,sha256=cYqgl-7Ix-xslL83N8Ae6kzAIKlpdd2qkMYciRBO4PA,1732
timm/data/readers/reader_hfds.py,sha256=IbHTx687uj0-hW-pPvKfxZhcGFrIEUj1tjJA2xyTFhc,2712
timm/data/readers/reader_hfids.py,sha256=BZ_ayvLFOct10lTpIw1z16y1IrEeV8HY4WLwKDZTIcU,8409
timm/data/readers/reader_image_folder.py,sha256=CqCwjmdpj93HG4AMTFewQN7N_TW1OW6vkgMk1Q22S4g,3508
timm/data/readers/reader_image_in_tar.py,sha256=ok4IDPkpuu_3RKxK83EEo7zfLPnqJq_x7CwDmepAf3E,9182
timm/data/readers/reader_image_tar.py,sha256=h0PlhmHU_1QzUy4p3psI9JoeaAao1_OBFK1_K6Re9e4,2644
timm/data/readers/reader_tfds.py,sha256=5daaqhJzYq7hbYBAeuF06lXCO7A0tPipHbJVMFMv-Nk,17909
timm/data/readers/reader_wds.py,sha256=ZINr0iB3xrz9Vim1dvobepUOpFyQZAP8SFdOobLDYkk,16976
timm/data/readers/shared_count.py,sha256=___RvLR-johRZ0Ins6iKVAkKU44LxU3o4H7E0B0b25o,303
timm/data/real_labels.py,sha256=xdexeoU_KE6c2HY0G81Ny2-T_iTQ_1HOWLj7R_WfAls,1800
timm/data/tf_preprocessing.py,sha256=xClMeZyUPx8YATBvVnzig0bDJNBa8ZqzcpmVWUwefWg,9169
timm/data/transforms.py,sha256=IGkJUgw044feTMkHXjsHT4RBLvFp8Tn7sjFzLw7TbI4,20120
timm/data/transforms_factory.py,sha256=enziKOl0De4pNf7uZ5eedlr-esUlkd7PEC_OKzdKOkY,21389
timm/layers/__init__.py,sha256=zTzHpZDbJams2pKkId8o_sW_bjS7Pd5vzymi7Im97ts,5101
timm/layers/__pycache__/__init__.cpython-312.pyc,,
timm/layers/__pycache__/_fx.cpython-312.pyc,,
timm/layers/__pycache__/activations.cpython-312.pyc,,
timm/layers/__pycache__/activations_me.cpython-312.pyc,,
timm/layers/__pycache__/adaptive_avgmax_pool.cpython-312.pyc,,
timm/layers/__pycache__/attention.cpython-312.pyc,,
timm/layers/__pycache__/attention2d.cpython-312.pyc,,
timm/layers/__pycache__/attention_pool.cpython-312.pyc,,
timm/layers/__pycache__/attention_pool2d.cpython-312.pyc,,
timm/layers/__pycache__/blur_pool.cpython-312.pyc,,
timm/layers/__pycache__/bottleneck_attn.cpython-312.pyc,,
timm/layers/__pycache__/cbam.cpython-312.pyc,,
timm/layers/__pycache__/classifier.cpython-312.pyc,,
timm/layers/__pycache__/cond_conv2d.cpython-312.pyc,,
timm/layers/__pycache__/config.cpython-312.pyc,,
timm/layers/__pycache__/conv2d_same.cpython-312.pyc,,
timm/layers/__pycache__/conv_bn_act.cpython-312.pyc,,
timm/layers/__pycache__/create_act.cpython-312.pyc,,
timm/layers/__pycache__/create_attn.cpython-312.pyc,,
timm/layers/__pycache__/create_conv2d.cpython-312.pyc,,
timm/layers/__pycache__/create_norm.cpython-312.pyc,,
timm/layers/__pycache__/create_norm_act.cpython-312.pyc,,
timm/layers/__pycache__/drop.cpython-312.pyc,,
timm/layers/__pycache__/eca.cpython-312.pyc,,
timm/layers/__pycache__/evo_norm.cpython-312.pyc,,
timm/layers/__pycache__/fast_norm.cpython-312.pyc,,
timm/layers/__pycache__/filter_response_norm.cpython-312.pyc,,
timm/layers/__pycache__/format.cpython-312.pyc,,
timm/layers/__pycache__/gather_excite.cpython-312.pyc,,
timm/layers/__pycache__/global_context.cpython-312.pyc,,
timm/layers/__pycache__/grid.cpython-312.pyc,,
timm/layers/__pycache__/grn.cpython-312.pyc,,
timm/layers/__pycache__/halo_attn.cpython-312.pyc,,
timm/layers/__pycache__/helpers.cpython-312.pyc,,
timm/layers/__pycache__/hybrid_embed.cpython-312.pyc,,
timm/layers/__pycache__/inplace_abn.cpython-312.pyc,,
timm/layers/__pycache__/interpolate.cpython-312.pyc,,
timm/layers/__pycache__/lambda_layer.cpython-312.pyc,,
timm/layers/__pycache__/layer_scale.cpython-312.pyc,,
timm/layers/__pycache__/linear.cpython-312.pyc,,
timm/layers/__pycache__/median_pool.cpython-312.pyc,,
timm/layers/__pycache__/mixed_conv2d.cpython-312.pyc,,
timm/layers/__pycache__/ml_decoder.cpython-312.pyc,,
timm/layers/__pycache__/mlp.cpython-312.pyc,,
timm/layers/__pycache__/non_local_attn.cpython-312.pyc,,
timm/layers/__pycache__/norm.cpython-312.pyc,,
timm/layers/__pycache__/norm_act.cpython-312.pyc,,
timm/layers/__pycache__/padding.cpython-312.pyc,,
timm/layers/__pycache__/patch_dropout.cpython-312.pyc,,
timm/layers/__pycache__/patch_embed.cpython-312.pyc,,
timm/layers/__pycache__/pool1d.cpython-312.pyc,,
timm/layers/__pycache__/pool2d_same.cpython-312.pyc,,
timm/layers/__pycache__/pos_embed.cpython-312.pyc,,
timm/layers/__pycache__/pos_embed_rel.cpython-312.pyc,,
timm/layers/__pycache__/pos_embed_sincos.cpython-312.pyc,,
timm/layers/__pycache__/selective_kernel.cpython-312.pyc,,
timm/layers/__pycache__/separable_conv.cpython-312.pyc,,
timm/layers/__pycache__/space_to_depth.cpython-312.pyc,,
timm/layers/__pycache__/split_attn.cpython-312.pyc,,
timm/layers/__pycache__/split_batchnorm.cpython-312.pyc,,
timm/layers/__pycache__/squeeze_excite.cpython-312.pyc,,
timm/layers/__pycache__/std_conv.cpython-312.pyc,,
timm/layers/__pycache__/test_time_pool.cpython-312.pyc,,
timm/layers/__pycache__/trace_utils.cpython-312.pyc,,
timm/layers/__pycache__/typing.cpython-312.pyc,,
timm/layers/__pycache__/weight_init.cpython-312.pyc,,
timm/layers/_fx.py,sha256=I09rf691nAww4uhEg7IMNYwcxMbzw0ryIfELF8lXq4w,2220
timm/layers/activations.py,sha256=xkvz11k2_Bx_CeOna1lRY0F10ew0mXc8ojersdz25ec,4880
timm/layers/activations_me.py,sha256=cPNk1XdvfOunn3Rd9ohNbRh8LtHw5-DPKYp04FMtBZU,5504
timm/layers/adaptive_avgmax_pool.py,sha256=58cPkSKechsJHJQvcfW_onZRMLntnDNduMitWjmqSMo,6583
timm/layers/attention.py,sha256=08xITsVA0GippETUQTC8XWySyXn1DPMdeWKl5lke81o,8659
timm/layers/attention2d.py,sha256=lQ9lelfchk67ngQUV9xEk2WuzGMc3Ff4tub3YPtsSkI,12849
timm/layers/attention_pool.py,sha256=qVIQ96OMtGbtZJypKFNCnUQ9V9aW2MxVuXgBN3b73SQ,3904
timm/layers/attention_pool2d.py,sha256=g4nQkZ1KE12K4WsO3yPzo0_-H0sOwo0r6hVJSopqHO0,11551
timm/layers/blur_pool.py,sha256=E4L6Zc74NekRbyZNdh4aHg7TX4t2-HPf9lKhwa5Hybw,3263
timm/layers/bottleneck_attn.py,sha256=HLuZbyep1Nf9Qq9Aei81kCzQMs6U1aQBQRLrOnjnkHo,6895
timm/layers/cbam.py,sha256=b6lo3KFOc88MV4ITw2pSokuvpLkAFpB9lNc1e20QdGI,4426
timm/layers/classifier.py,sha256=ZNQSi2xX--beMr4VWJeR-hU4eItUHNOvLfUT3-o-4rk,10408
timm/layers/cond_conv2d.py,sha256=y8QbN_236fIiY-jNLZMPRxP-M83f2PMAqwgcg_DMfos,5250
timm/layers/config.py,sha256=WexEM1UE7XDFw6AqfPkdlDxkNe6HwQBgY3MINXg_pOU,4577
timm/layers/conv2d_same.py,sha256=mnp6LwlCT7ZUcf0WutBd6M78VFWxAPgCels-D9DaQqU,3282
timm/layers/conv_bn_act.py,sha256=A9-TW3D7LQW1hVFIyHrtvAdszEausR5ZbsEO2FIvvBk,2907
timm/layers/create_act.py,sha256=iKYwDDXRdcY6N5Q75xSpt2d-3wAUiYl6lXRvcg1pWxk,4549
timm/layers/create_attn.py,sha256=d9-j6iPcFPpC2o7_DW4QLKKP1KWZBhZn6-AHH-iSfh4,3514
timm/layers/create_conv2d.py,sha256=darxUzkvPYUxguuJ_WJd1PepkxOK4if5JwJzC5IC85k,1622
timm/layers/create_norm.py,sha256=_lQNdGKi6QMbPDqUGl-prTfBqJBD5l1xEJswRDNqwVY,2062
timm/layers/create_norm_act.py,sha256=dSPVkbosPHrG7-N0SD8dxer1NXuqlT27R13RCAdEl1A,4668
timm/layers/drop.py,sha256=HocUkCNINxvSRHV0dQR7zpeRiJQG_Kkk8_ww2AHK2Zg,6973
timm/layers/eca.py,sha256=MiVhboDUqLUfeubpypWfaR3LMLHwgLCNsWO3iemcQFs,6386
timm/layers/evo_norm.py,sha256=mOJu-pMlBkVGjp3aKN0lhjnuED3lXLETNqbJIHpSSSA,13862
timm/layers/fast_norm.py,sha256=co_dAE7ZRDRbZAhC1eREjB9s3zBFxCqcNvqIaVMGNms,8196
timm/layers/filter_response_norm.py,sha256=mDJ3nbu5nicP-5actvDiZa4HYd393Vq-_06ZTca5d4w,2540
timm/layers/format.py,sha256=i02NLXbWXPv4WJCSUF4MnSjQp699-UGr5Z3rnMZk364,1109
timm/layers/gather_excite.py,sha256=53DHt6cySjPqd9NW3voZuhw8b9nUzvsG9NVl_D-9NAo,3824
timm/layers/global_context.py,sha256=aZWvij4J-T5I1rdTK725D6R0fDuJyYPDaXvl36QMmkw,2445
timm/layers/grid.py,sha256=lMM8bM3ggxunvQFqQCB943SZAfY7Nw04w-lFaMBkxt8,1624
timm/layers/grn.py,sha256=dxLWn-V48OiFlKLLKaU8Zt0mdBcR_AOg0mh1i8tmHKY,1319
timm/layers/halo_attn.py,sha256=zMJkf9S-ocCvrfvWOe0I97UHTpEQIkP381DON3OXm-c,10662
timm/layers/helpers.py,sha256=9VLqID8jjdw_Un270F3rQLvNz9vQMhN9mts7kk_Ma_Y,1053
timm/layers/hybrid_embed.py,sha256=6IjQvGASNtME8rhr66L7l-ljFXmfaYB1B6Lbb9TarGg,9975
timm/layers/inplace_abn.py,sha256=p5gbHK14tMOx2A1XZkrudhcWtuli301W3ZIiGVOAr60,3441
timm/layers/interpolate.py,sha256=OsobWqF1CrpLe6YkXLmRzASbAsw_uzztyqsBZ6xUYrs,2439
timm/layers/lambda_layer.py,sha256=-jB-uYoYqk0QjStAhaec30uyEAWp64N96_Bw33oY8H8,5958
timm/layers/layer_scale.py,sha256=66PdUvfjgTxKXjjFpoETDnDRjQ6Dt1m8n-ysnk2ZFAM,1021
timm/layers/linear.py,sha256=baS2Wpl0vYELnvhnQ6Lw65jVotaJz5iGbroJJ9JmIRM,743
timm/layers/median_pool.py,sha256=b02v36VGvs_gCY9NhVwU7-mglcXJHzrJVzcEpEUuHBI,1737
timm/layers/mixed_conv2d.py,sha256=mRSmAUtpgHya_RdnUq4j85K5QS7JFTdSPUXOUTKgpmA,1843
timm/layers/ml_decoder.py,sha256=Kk7JBS8TIlVWsFE8o9iFgN70JzIOJNmJqXrWWvCxpb0,6701
timm/layers/mlp.py,sha256=Rj4cTW9aI0CuYxzRibMigsWesgEMRyKrdfMCTg4WxP4,8826
timm/layers/non_local_attn.py,sha256=rYuvBb3SgCCYCRie8MJJJ7_h_rtMVfP8sjkSxLqpILM,6284
timm/layers/norm.py,sha256=D7P9ryS1Wm1kFcC9EH1yaPs1n2jmwivjwBSvLBYO9Qk,20152
timm/layers/norm_act.py,sha256=SSW2LA2aq_6bJ6ib2dBoaia4Qzys52nrGTsCEG9MGgI,25924
timm/layers/padding.py,sha256=7ToIOAk5HiOs8KpiPzqpNQI8UiGQHIbwWWkxknNnIZo,3471
timm/layers/patch_dropout.py,sha256=R5v6e2tntFch_JcvEELlYdZ5gpEwyr7u-v34J_c6q00,1778
timm/layers/patch_embed.py,sha256=ePLWr7o79xoLilHPsG4DSwivBwj0pJUJUR5LQZ5EjNo,23830
timm/layers/pool1d.py,sha256=dYv5Ov_oQo7csLifmsxeRevQ-ExHc5rwT3-b18PphN0,670
timm/layers/pool2d_same.py,sha256=AsUO2DorhgVHPSLEjiS9NQyVeqTHBiw-4pL75P-fx14,3229
timm/layers/pos_embed.py,sha256=jlvtpVKJ_3pKkfre5upBNJObhgZCcj-PtdyCA5_MTTc,2681
timm/layers/pos_embed_rel.py,sha256=xIkPKYHQBxfXVr3-1xWy6-K8fKM-b9DoNS0WOHkzvfA,19370
timm/layers/pos_embed_sincos.py,sha256=t4gIckpiUpJgB9a7ehjisCAnIdwnYkQsmEFuqnPLdsU,22101
timm/layers/selective_kernel.py,sha256=oLsbqh3HYVjg8lW4AbKQplW0k-xHlbIlOgAMF6r4brQ,5383
timm/layers/separable_conv.py,sha256=staVZPP-BxtO3q0Ka3_VnI1M1e-xtNAUUACP81rhF_Y,2620
timm/layers/space_to_depth.py,sha256=BwTu9tEamsmqF-DHdkHgWBv8Paf3_CE8v-IGZiLU1Hc,1068
timm/layers/split_attn.py,sha256=Cl2gx0lNVosX2zgieLgf_FtqUwuwtGKTyxSoWyvejeg,3076
timm/layers/split_batchnorm.py,sha256=4ghGtliK5z0ZnzR29zJB_rN9BJPiGuy1PSltmVyF5Ww,3441
timm/layers/squeeze_excite.py,sha256=YrJELkYE5cB1c-r4Ww9omezUp3dugbgz-qN8XsTbc3I,4327
timm/layers/std_conv.py,sha256=dnK68bSgsd8SyB9STstaBQhF5ixDsBiGSWuIj5DHreA,5978
timm/layers/test_time_pool.py,sha256=Z5lPvVLI4IYqrJLGQhgJfxPasug9nts1y6mDD_rznBQ,1996
timm/layers/trace_utils.py,sha256=cbZufOaGKmhTGEMc52QAnqzGRTfn4vvzqsAOJaLKJQ8,335
timm/layers/typing.py,sha256=UYrThz9-g8PlmXr7LZutKft6seFaFVp_ZrD0ZGV2aP4,163
timm/layers/weight_init.py,sha256=Fgwow3t2QF6i67mUQQhYT1JYZuM8VjcY9oYmFfT09Ng,6208
timm/loss/__init__.py,sha256=iCNB9bUAf69neNe1_XO0eeg1QXuxu6jRTAuy4V9yFL8,245
timm/loss/__pycache__/__init__.cpython-312.pyc,,
timm/loss/__pycache__/asymmetric_loss.cpython-312.pyc,,
timm/loss/__pycache__/binary_cross_entropy.cpython-312.pyc,,
timm/loss/__pycache__/cross_entropy.cpython-312.pyc,,
timm/loss/__pycache__/jsd.cpython-312.pyc,,
timm/loss/asymmetric_loss.py,sha256=3BajT94OJslw-MSrqQLRB67SLT2pbhb2vqj0CtOKN6w,3240
timm/loss/binary_cross_entropy.py,sha256=9AgASCvD-URGZS1E6XyaaYBxf0UOx8AHHKC7tTLTQzU,2483
timm/loss/cross_entropy.py,sha256=XDE19FnhYjeudAerb6UulIID34AmZoXQ1CPEAjEkCQM,1145
timm/loss/jsd.py,sha256=MFe8H_JC1srFE_FKinF7jMVIQYgNWgeT7kZL9WeIXGI,1595
timm/models/__init__.py,sha256=_6nBmU4Ulqfpj_Mio8aq6yWgj63vMpDZdCNwURS41_Q,5207
timm/models/__pycache__/__init__.cpython-312.pyc,,
timm/models/__pycache__/_builder.cpython-312.pyc,,
timm/models/__pycache__/_efficientnet_blocks.cpython-312.pyc,,
timm/models/__pycache__/_efficientnet_builder.cpython-312.pyc,,
timm/models/__pycache__/_factory.cpython-312.pyc,,
timm/models/__pycache__/_features.cpython-312.pyc,,
timm/models/__pycache__/_features_fx.cpython-312.pyc,,
timm/models/__pycache__/_helpers.cpython-312.pyc,,
timm/models/__pycache__/_hub.cpython-312.pyc,,
timm/models/__pycache__/_manipulate.cpython-312.pyc,,
timm/models/__pycache__/_pretrained.cpython-312.pyc,,
timm/models/__pycache__/_prune.cpython-312.pyc,,
timm/models/__pycache__/_registry.cpython-312.pyc,,
timm/models/__pycache__/beit.cpython-312.pyc,,
timm/models/__pycache__/byoanet.cpython-312.pyc,,
timm/models/__pycache__/byobnet.cpython-312.pyc,,
timm/models/__pycache__/cait.cpython-312.pyc,,
timm/models/__pycache__/coat.cpython-312.pyc,,
timm/models/__pycache__/convit.cpython-312.pyc,,
timm/models/__pycache__/convmixer.cpython-312.pyc,,
timm/models/__pycache__/convnext.cpython-312.pyc,,
timm/models/__pycache__/crossvit.cpython-312.pyc,,
timm/models/__pycache__/cspnet.cpython-312.pyc,,
timm/models/__pycache__/davit.cpython-312.pyc,,
timm/models/__pycache__/deit.cpython-312.pyc,,
timm/models/__pycache__/densenet.cpython-312.pyc,,
timm/models/__pycache__/dla.cpython-312.pyc,,
timm/models/__pycache__/dpn.cpython-312.pyc,,
timm/models/__pycache__/edgenext.cpython-312.pyc,,
timm/models/__pycache__/efficientformer.cpython-312.pyc,,
timm/models/__pycache__/efficientformer_v2.cpython-312.pyc,,
timm/models/__pycache__/efficientnet.cpython-312.pyc,,
timm/models/__pycache__/efficientvit_mit.cpython-312.pyc,,
timm/models/__pycache__/efficientvit_msra.cpython-312.pyc,,
timm/models/__pycache__/eva.cpython-312.pyc,,
timm/models/__pycache__/factory.cpython-312.pyc,,
timm/models/__pycache__/fasternet.cpython-312.pyc,,
timm/models/__pycache__/fastvit.cpython-312.pyc,,
timm/models/__pycache__/features.cpython-312.pyc,,
timm/models/__pycache__/focalnet.cpython-312.pyc,,
timm/models/__pycache__/fx_features.cpython-312.pyc,,
timm/models/__pycache__/gcvit.cpython-312.pyc,,
timm/models/__pycache__/ghostnet.cpython-312.pyc,,
timm/models/__pycache__/hardcorenas.cpython-312.pyc,,
timm/models/__pycache__/helpers.cpython-312.pyc,,
timm/models/__pycache__/hgnet.cpython-312.pyc,,
timm/models/__pycache__/hiera.cpython-312.pyc,,
timm/models/__pycache__/hieradet_sam2.cpython-312.pyc,,
timm/models/__pycache__/hrnet.cpython-312.pyc,,
timm/models/__pycache__/hub.cpython-312.pyc,,
timm/models/__pycache__/inception_next.cpython-312.pyc,,
timm/models/__pycache__/inception_resnet_v2.cpython-312.pyc,,
timm/models/__pycache__/inception_v3.cpython-312.pyc,,
timm/models/__pycache__/inception_v4.cpython-312.pyc,,
timm/models/__pycache__/levit.cpython-312.pyc,,
timm/models/__pycache__/mambaout.cpython-312.pyc,,
timm/models/__pycache__/maxxvit.cpython-312.pyc,,
timm/models/__pycache__/metaformer.cpython-312.pyc,,
timm/models/__pycache__/mlp_mixer.cpython-312.pyc,,
timm/models/__pycache__/mobilenetv3.cpython-312.pyc,,
timm/models/__pycache__/mobilenetv5.cpython-312.pyc,,
timm/models/__pycache__/mobilevit.cpython-312.pyc,,
timm/models/__pycache__/mvitv2.cpython-312.pyc,,
timm/models/__pycache__/naflexvit.cpython-312.pyc,,
timm/models/__pycache__/nasnet.cpython-312.pyc,,
timm/models/__pycache__/nest.cpython-312.pyc,,
timm/models/__pycache__/nextvit.cpython-312.pyc,,
timm/models/__pycache__/nfnet.cpython-312.pyc,,
timm/models/__pycache__/pit.cpython-312.pyc,,
timm/models/__pycache__/pnasnet.cpython-312.pyc,,
timm/models/__pycache__/pvt_v2.cpython-312.pyc,,
timm/models/__pycache__/rdnet.cpython-312.pyc,,
timm/models/__pycache__/registry.cpython-312.pyc,,
timm/models/__pycache__/regnet.cpython-312.pyc,,
timm/models/__pycache__/repghost.cpython-312.pyc,,
timm/models/__pycache__/repvit.cpython-312.pyc,,
timm/models/__pycache__/res2net.cpython-312.pyc,,
timm/models/__pycache__/resnest.cpython-312.pyc,,
timm/models/__pycache__/resnet.cpython-312.pyc,,
timm/models/__pycache__/resnetv2.cpython-312.pyc,,
timm/models/__pycache__/rexnet.cpython-312.pyc,,
timm/models/__pycache__/selecsls.cpython-312.pyc,,
timm/models/__pycache__/senet.cpython-312.pyc,,
timm/models/__pycache__/sequencer.cpython-312.pyc,,
timm/models/__pycache__/shvit.cpython-312.pyc,,
timm/models/__pycache__/sknet.cpython-312.pyc,,
timm/models/__pycache__/starnet.cpython-312.pyc,,
timm/models/__pycache__/swiftformer.cpython-312.pyc,,
timm/models/__pycache__/swin_transformer.cpython-312.pyc,,
timm/models/__pycache__/swin_transformer_v2.cpython-312.pyc,,
timm/models/__pycache__/swin_transformer_v2_cr.cpython-312.pyc,,
timm/models/__pycache__/tiny_vit.cpython-312.pyc,,
timm/models/__pycache__/tnt.cpython-312.pyc,,
timm/models/__pycache__/tresnet.cpython-312.pyc,,
timm/models/__pycache__/twins.cpython-312.pyc,,
timm/models/__pycache__/vgg.cpython-312.pyc,,
timm/models/__pycache__/visformer.cpython-312.pyc,,
timm/models/__pycache__/vision_transformer.cpython-312.pyc,,
timm/models/__pycache__/vision_transformer_hybrid.cpython-312.pyc,,
timm/models/__pycache__/vision_transformer_relpos.cpython-312.pyc,,
timm/models/__pycache__/vision_transformer_sam.cpython-312.pyc,,
timm/models/__pycache__/vitamin.cpython-312.pyc,,
timm/models/__pycache__/volo.cpython-312.pyc,,
timm/models/__pycache__/vovnet.cpython-312.pyc,,
timm/models/__pycache__/xception.cpython-312.pyc,,
timm/models/__pycache__/xception_aligned.cpython-312.pyc,,
timm/models/__pycache__/xcit.cpython-312.pyc,,
timm/models/_builder.py,sha256=Qkr9UnyxeA1zxQ8jAs0JRfOxWJqGBCfEUiHU8Tbilwg,21917
timm/models/_efficientnet_blocks.py,sha256=JRf3iopVbArPwNyiSJbhNASS6eeXwmJpGbNkKCRLZoY,26316
timm/models/_efficientnet_builder.py,sha256=3GKkift_ebPGWAQC2abHZ_i5-KjtbwcaTngnWJUBUKg,23834
timm/models/_factory.py,sha256=MKbuzaunUK9HDPrjGWGG_WRwUscjOKIckokUqsBhY-Q,6338
timm/models/_features.py,sha256=XaXGeWZvUdUiFrHl2I5SiYtBAcQjfiCd95ddMm8gNZQ,19778
timm/models/_features_fx.py,sha256=DxS8JkjdO9iVBeXhGYg6T0ceSOVuRLNzJSqDDRrW0zI,3279
timm/models/_helpers.py,sha256=gn07gCs6Ja5eCZ2QhIkMvZBhd8UaNWVg_pQ-mgBZR2w,8110
timm/models/_hub.py,sha256=_BnXm3VvkVa9Q9hoQ-mdlMeZDaZyOCeuR_57-DOX_zo,19490
timm/models/_manipulate.py,sha256=lQ9vLosBbrd-_QC3JmcjhNDk4ZTPLxvFC3xUqk1iykI,11132
timm/models/_pretrained.py,sha256=uS95ANJTn4eYOkKdLzuZUDz31BTn0oHMFkY-qs2rCjE,3525
timm/models/_prune.py,sha256=r0LJI-UCYSDZrEAej8lN2OeDJxBEey9tA6FBh8uZyH4,4325
timm/models/_pruned/ecaresnet101d_pruned.txt,sha256=1zA7XaxsTnFJxZ9PMbfMVST7wPSQcAV-UzSgdFfGgYY,8734
timm/models/_pruned/ecaresnet50d_pruned.txt,sha256=J4AlTwabaSB6-XrINCPCDWMiM_FrdNjuJN_JJRb89WE,4520
timm/models/_pruned/efficientnet_b1_pruned.txt,sha256=pNDm1EENJYMT8-GjXZ3kXWCXADLDun-4jfigh74RELE,18596
timm/models/_pruned/efficientnet_b2_pruned.txt,sha256=e_oaSVM-Ux3NMVARynJ74YwjzxuBAX_w7kzOw9Ml3gM,18676
timm/models/_pruned/efficientnet_b3_pruned.txt,sha256=A1DJEwjEmrg8oUr0QwzwBkdAJV3dVeUFjnO9pNC_0Pg,21133
timm/models/_registry.py,sha256=TI0rs9L-YXTRA-8zuxrNWETzrdGwD5PekW2UUoUajbg,14465
timm/models/beit.py,sha256=Hpa7T03HfV9i6WsLzzV9EVDQxvb50FehAAIm0WxuCY4,39438
timm/models/byoanet.py,sha256=SlKLshjI_F8vXWVhWPo6nCxOQMeVs_Bn7Z79xyx-n0Q,19754
timm/models/byobnet.py,sha256=LQ6R-haAtGpgg1qJhNPYnELJVZlwvVXudhzXXov-lxg,111174
timm/models/cait.py,sha256=YJb8yQc85DIYnjvroC_1LaYtn85e-qDBvXAbUmUnqvg,21535
timm/models/coat.py,sha256=m_jGoKWZeoVa7zCjhrPoGcSkW43chjV296O9OUaqrcs,29955
timm/models/convit.py,sha256=UIEc1iUUWpkYpium6sxveqY6I_83rMpoND-Peva2G_Q,15306
timm/models/convmixer.py,sha256=p_efUDPV9LkAMWfher9QDWl_Qvd_RLK5R4eNGJw_QmI,4676
timm/models/convnext.py,sha256=Xmq04NmFFlkAtl0T6ZuHCPiIRmFcsFr0fkeNeKE7Pcg,59101
timm/models/crossvit.py,sha256=tsf_KGU1r4Ib4-eXkTxnXRcW7PCyEdYCCCRs6yo_lq8,24304
timm/models/cspnet.py,sha256=0baoP3XYKhZu-lxuC4RhhumsPDBj8D8H_HCSQO05cFo,40257
timm/models/davit.py,sha256=J5xvzMm5H2IjW1V-xNrXV0RW7z2W1fl31oiSPpNwTUs,30381
timm/models/deit.py,sha256=GzvD1X3bMfG70OKENyAERZhFUhdg3p0wJshDfOl4zHQ,18631
timm/models/densenet.py,sha256=ZbVBok6b6cQGT3zH5dKQWYCuqHXpTyp2QyrVmRV7I-U,20853
timm/models/dla.py,sha256=WF-4lNbbmIdSliK7H9SlfmV1dPivfLuVXf7N51i3eGY,18616
timm/models/dpn.py,sha256=sZeOD38MT4Qnxy-nlckc-pERl_hYHsWxYS62GRj7XNY,13642
timm/models/edgenext.py,sha256=nbcrHVXk_x0c9VvE-2lhP3ixpN3ECNPoWJ3ue7AjjLM,23494
timm/models/efficientformer.py,sha256=qCgtbPMOW1Egxck4HeLTHntO12tUXrkm4-BjBBLI01I,21967
timm/models/efficientformer_v2.py,sha256=3sAmXHA1_TXs7-XQP_vbtGvqgrmibjw65wEG9ZShkrc,27421
timm/models/efficientnet.py,sha256=gllV7rAVGsBZZRm-iUiUVrZoUqzMkT0CrZp-aNuLxuA,125168
timm/models/efficientvit_mit.py,sha256=GBNQ98XbBbooKHmaAyxUBW10VTxA0c5Cf2mVFfdR5Ns,37887
timm/models/efficientvit_msra.py,sha256=kewdpDmMphPiyMuAYxFkjOLAgpkQ17Bc_8SDEGzXbc8,25769
timm/models/eva.py,sha256=tTpe393WVD_5XYJlr3mSm4MsBz5zmqJHTQTyW7JAfJI,84430
timm/models/factory.py,sha256=vbXTq3VsGH8H71RlTi0jLMemHpNSn50fTT_4K5kNsb0,145
timm/models/fasternet.py,sha256=H0t0KDb61uVr37pKJlFfHpfF_GyE85b2w8cTei_YD8k,18097
timm/models/fastvit.py,sha256=qrTWmXQHuP5-xkfj7c4_AINUD53Gz9WfJwt9JWJoToQ,57230
timm/models/features.py,sha256=l-1pBa36r0ImSRnVRd6kLYOdoMx8TvUMCgbIkQAooT0,146
timm/models/focalnet.py,sha256=QTxNRxfUZZU0SQrVIf8Y_IvOzfEa1P1h5YTjStgOhu8,26667
timm/models/fx_features.py,sha256=-xyHnT-WpuVFJPViVkhpdRFgkw5GNXSs_RzFYDHkIHk,149
timm/models/gcvit.py,sha256=vEnvYatCTnuOrblN3QIjcrLYwgRQOQ91n62GT3DsRTQ,23675
timm/models/ghostnet.py,sha256=DGPMi0u3GxdZ2kCuGpBq-FIIr4TYnuJjPjnQQs2MX9Y,35254
timm/models/hardcorenas.py,sha256=MdMhROScNZ92ysLQZP6AxRz1ND0o3hn920FLLbGFtt8,7697
timm/models/helpers.py,sha256=J81ZF-lhp8L3opH8b02Iob-VFMm_36leF1W5p3RLWs4,218
timm/models/hgnet.py,sha256=QcpKCT3pkwg42GMIpG6VNMNbEM-v_ehlRy0UVhsezpI,25750
timm/models/hiera.py,sha256=8bHcjFbKFS5h_vejitRwZYaL-6naZ9q6CgOgImYg3m0,36760
timm/models/hieradet_sam2.py,sha256=lsCtxogWAvwbJdSk-FhVyCq8iXSQqQhV9B2DPOm1HwU,24343
timm/models/hrnet.py,sha256=Cq1s9ZynNKFGjvLgiX6WE9SI4F8uMMJwVvf6S7TkdNk,33336
timm/models/hub.py,sha256=WdUls0bZlTfAtQ447Wk5W-jWE074bkzndOflKPJ1uyo,141
timm/models/inception_next.py,sha256=FM38hxxl2zkfDQIEOLnxT-uJE9DnyvHHH1FLnOFhVmQ,17333
timm/models/inception_resnet_v2.py,sha256=wW2C8YMU4F_9HB4EdeL6suVOlwG8jSFgT_79s2VmZeU,12094
timm/models/inception_v3.py,sha256=dabYq6QH13psqnXnxDFb9li356Bm7tzSSipLmHBfISE,17223
timm/models/inception_v4.py,sha256=V8wrXW0YIUnxXYxwL6RO4NN6gm3JTLJjwDV67-_MkHg,13593
timm/models/layers/__init__.py,sha256=fLlPJlSRaEjjYnW7mdBtwu5XIlchP6or3EZlZEzGFMs,3369
timm/models/layers/__pycache__/__init__.cpython-312.pyc,,
timm/models/levit.py,sha256=IuZrIlXFuIohzsVmqRzx55u4gI1kA7LI92kpwbFwd2s,35115
timm/models/mambaout.py,sha256=LP5zMmxl3byLsWMRW4uqcCs5T4eDz4wW4zNahVdxg7s,22940
timm/models/maxxvit.py,sha256=7qW2Q61jsRBvd9oXxwR-6kgHZ0VmQFAf5yXRbQthUyI,99850
timm/models/metaformer.py,sha256=ySKyWafkFuttpU5JmHuMn9oWEHBHPcu3bPLwNAHUTDg,37650
timm/models/mlp_mixer.py,sha256=SwMHZPCdsZxGr1wirvoRewNtuVAVjLZCdJUFFQI9BvQ,32522
timm/models/mobilenetv3.py,sha256=EoBSUz8f5RsNheR6WyVmWu1jB4HHRO3RsCpw0rR8Hg0,61196
timm/models/mobilenetv5.py,sha256=p-xRXQnttw0KY8rvaNORYZ-IZ1cLcjcc0hxPHRiZe0A,32245
timm/models/mobilevit.py,sha256=ZHbGPG3vFhFCI0f3zLWzH2Jqiasr_Sacah_1MBi9KI8,25734
timm/models/mvitv2.py,sha256=dA34Ea8Nj5lRL3TElP0aaiPlskuwwsMcc595OVz74K8,39198
timm/models/naflexvit.py,sha256=AoAIqdwgeEQqwIhEteI6QysKVnjJWHVjarR4vWlFSiI,73936
timm/models/nasnet.py,sha256=mqwOp5LiJHSd_YOf-9zI0Ql7U6QH_ACJ2uWHHgnshPg,26670
timm/models/nest.py,sha256=SxH7nrzSMK3BA00OOrXu7o_H-zCSnjeR3wf8urIoiDc,24215
timm/models/nextvit.py,sha256=i8tUJ6CM97ToNotKXQHC8Y8BKTVvsnpRATqaJtFF21k,25441
timm/models/nfnet.py,sha256=lMyAiiZm7i-nQ5uHrHmZ7AUGlzV3OtWw8VgB9WS9fak,45201
timm/models/pit.py,sha256=RMUMjVNHieyWyS7PHZRi0k4-xOz3bgR2fkrEM5HR1mo,17573
timm/models/pnasnet.py,sha256=2uw2mdk-8hywDS42tAmqgwiWZnaGCPjUQzqZWK6LOnU,15406
timm/models/pvt_v2.py,sha256=9ro9zXSA5Xu4xDhcRxYRvap0y6HUC30TNvRpKbjp1x0,19462
timm/models/rdnet.py,sha256=L_dcEYdgetL8Us8if9JE4W2UYqkR7HrXs3LJwXpKG7I,19789
timm/models/registry.py,sha256=JdbWI-bGwVhzgjdci73SMw8JQsjHEZYaS-EtutPz9g0,146
timm/models/regnet.py,sha256=KdPKEJf7Gr6xbKexXBNOlJAzWHIeW-yxjKIiEqVaJII,55453
timm/models/repghost.py,sha256=95h3ODF9Zd_J9rTWrdVubHUqt664e03Lx604TX5hh2s,19279
timm/models/repvit.py,sha256=FWSN9CzE22uez5OrmwfDkZZ-heA-UFnQTPuQbpO4gc8,18748
timm/models/res2net.py,sha256=PZeI435TG16x-_Zynvqdwfcvwy4b_1_9ydfu_vyBzOA,7691
timm/models/resnest.py,sha256=65DB4DAErNleuJ6VTVb9HfSM7QFWsxmTs-HT-e6uEIk,9635
timm/models/resnet.py,sha256=2nItkKY6fRmG7EBoC_heF4-wVCb_DPvgC0J0J4ip-XE,102941
timm/models/resnetv2.py,sha256=PWSvQN7ZW7yk8heM0cUAnzy_GEL9qTYQA4ewtnTxgg0,44866
timm/models/rexnet.py,sha256=-wiL3X4B2vPxPpAupbjIalUktjOxkZHjzRDVuk81K7k,19840
timm/models/selecsls.py,sha256=EWrF1DRntUEcJIFvEOSgfMTT2Hp42JX_-WBbOeV6l9Y,13271
timm/models/senet.py,sha256=RJUbiZt47tLreLZ9L_QMZMjIMVny1NCOmkMdzJ9RkA0,18268
timm/models/sequencer.py,sha256=Wb7HK4_WuPx51Mfw-XgCyXUxNpEc6lyNaeQ16YKuCQo,17310
timm/models/shvit.py,sha256=SXLgNM6s2byaoWFNeXupLbYziM53s7vU2oS_G7AqD4I,18864
timm/models/sknet.py,sha256=gXpl2gfrYmKu0LnqtWeY6NJm8sB3ITLNiZ_ozfA7J4w,8807
timm/models/starnet.py,sha256=uAzgBCQeEy9_SXb_p9vI9Ws0Qs82fF2fCcU02p2R_jA,12981
timm/models/swiftformer.py,sha256=UY-gPf7nA4Hxw1KtGjFzw0SjQeT0Lz7ss2dSug2nmMM,22500
timm/models/swin_transformer.py,sha256=tKNmbaspJVVjBMlAXW83IXut-R2XDVL_Hp9wqe28t7w,46304
timm/models/swin_transformer_v2.py,sha256=AR9it9bG7j9NFTJOfAyoG-z__lHcYbSAoY1q-3t_JPI,49100
timm/models/swin_transformer_v2_cr.py,sha256=56Rh4UYYYoClZ7SpN8kPePHPeQsqRIX791EodnysMKQ,47699
timm/models/tiny_vit.py,sha256=4Ca_KPWq360_JT-VX6DsEjt2MOtD3cpZLArF9Njag-M,26207
timm/models/tnt.py,sha256=FC77Le9K-lQIrcAxZWAAIrWv17jsu6wP6Y-kjGuJXqo,21298
timm/models/tresnet.py,sha256=QwGJIVEcxvl_rRMmwkh1sZTaa6cd1V_WLE3V8Q-7rcE,15145
timm/models/twins.py,sha256=TCi99mBJ6fcXji5MTa0MHGAAd9fkiyP0Q9AsnbNRP4k,21973
timm/models/vgg.py,sha256=j6JBaT_6ago-1A9Ey9btYC97cXaUi50G1IZAYwKw9as,14318
timm/models/visformer.py,sha256=2cme-3zY5_X-MZXSjBcwwip8GkHbd3xyT9f16V7LA8o,19031
timm/models/vision_transformer.py,sha256=02KnrT9WdoM-BNKNXgIiDlcSK9__8w-8sdcRgLTeRAc,190543
timm/models/vision_transformer_hybrid.py,sha256=Y3WZTR3wZfcQV1XugTIvjEukQF-98pIA6vagMJkyD6w,17957
timm/models/vision_transformer_relpos.py,sha256=Jdz1d4f3r5JAgxWQD8SR3CrYKZFpaxP7Rmo2nnC0sMg,28652
timm/models/vision_transformer_sam.py,sha256=gWyuTyPFEdB-g1OYstsxkWNLsJz6P4XP6YeWREOqVGo,28448
timm/models/vitamin.py,sha256=W6qCJndgfhTWJ_bYzOzHx1ZJczZfMGLXUgFRNpHPyfA,20580
timm/models/volo.py,sha256=qQ1QCLmsRIBDmREj3GaietB8u2vaRtndvaVMqjE2MOE,47440
timm/models/vovnet.py,sha256=gXYLIrbct53Tjqw8EnJTllx31JOVKVziYJzbmbXSi_E,18142
timm/models/xception.py,sha256=xj6Dxknlhhj2nYROEZoj45gibQROW5m9vB-XIDH3oyE,8147
timm/models/xception_aligned.py,sha256=YUO9bY4UAwXjevr2IUj8wY8crMNHav9UVRzEF1Jg-Jg,15453
timm/models/xcit.py,sha256=BqJ6VagYy4zdERWROVOWnynvg9XWAToyQcTlWLkR1QE,41846
timm/optim/__init__.py,sha256=ytQuzsLI-M2fNs7faB_LGzz6DAksi_EGEDxkem51wb8,1198
timm/optim/__pycache__/__init__.cpython-312.pyc,,
timm/optim/__pycache__/_optim_factory.cpython-312.pyc,,
timm/optim/__pycache__/_param_groups.cpython-312.pyc,,
timm/optim/__pycache__/_types.cpython-312.pyc,,
timm/optim/__pycache__/adabelief.cpython-312.pyc,,
timm/optim/__pycache__/adafactor.cpython-312.pyc,,
timm/optim/__pycache__/adafactor_bv.cpython-312.pyc,,
timm/optim/__pycache__/adahessian.cpython-312.pyc,,
timm/optim/__pycache__/adamp.cpython-312.pyc,,
timm/optim/__pycache__/adamw.cpython-312.pyc,,
timm/optim/__pycache__/adan.cpython-312.pyc,,
timm/optim/__pycache__/adopt.cpython-312.pyc,,
timm/optim/__pycache__/kron.cpython-312.pyc,,
timm/optim/__pycache__/lamb.cpython-312.pyc,,
timm/optim/__pycache__/laprop.cpython-312.pyc,,
timm/optim/__pycache__/lars.cpython-312.pyc,,
timm/optim/__pycache__/lion.cpython-312.pyc,,
timm/optim/__pycache__/lookahead.cpython-312.pyc,,
timm/optim/__pycache__/madgrad.cpython-312.pyc,,
timm/optim/__pycache__/mars.cpython-312.pyc,,
timm/optim/__pycache__/nadam.cpython-312.pyc,,
timm/optim/__pycache__/nadamw.cpython-312.pyc,,
timm/optim/__pycache__/nvnovograd.cpython-312.pyc,,
timm/optim/__pycache__/optim_factory.cpython-312.pyc,,
timm/optim/__pycache__/radam.cpython-312.pyc,,
timm/optim/__pycache__/rmsprop_tf.cpython-312.pyc,,
timm/optim/__pycache__/sgdp.cpython-312.pyc,,
timm/optim/__pycache__/sgdw.cpython-312.pyc,,
timm/optim/_optim_factory.py,sha256=_84lwS_7dSs_nHP0hId1tb9fRMyPa1L2mR6-lBj-Iok,45859
timm/optim/_param_groups.py,sha256=CRTOFwwWFn1am8tj2qSClYuLA-PDIA-QxqwO9rnz1xI,4468
timm/optim/_types.py,sha256=z1q5C9rQhAai14NdmohlqVfpf0ayw3XWoXMpyfKeQ34,655
timm/optim/adabelief.py,sha256=Ow94BI17AtaVl12vWXzsVgxUNqt92xpJ8XNPe60TbfQ,10034
timm/optim/adafactor.py,sha256=qZlWiCYZrPQtwPEooe9aI2FiEug5GSHHlj2mQuSy_cs,9969
timm/optim/adafactor_bv.py,sha256=0x0-0Cu560CACrJts7VKLU78rVsv5z3vBupuso2QadU,12904
timm/optim/adahessian.py,sha256=ecdHsfceF66JYwMxzWqwIzanY3nDzaBUHOJUC-lq8Kg,6716
timm/optim/adamp.py,sha256=4az5WKmmKYDGYoU5K4xGXuGpFL2VcPen_HKkw6X0MTo,3742
timm/optim/adamw.py,sha256=Wv9WmNQwEtRr-O9zdZZVy-2_HfqO3zrrZ9n2xY9Xpng,16657
timm/optim/adan.py,sha256=MzPHfhv75ky7zmPnOkq6hMqFx2rmmvxjQmqL4mvko7A,11747
timm/optim/adopt.py,sha256=lFxq5ZTyrczjUtwX3L2oRZQmRTg_i_3du2DX_1qNHYw,19510
timm/optim/kron.py,sha256=o6yDhmmHUXPcS7oRMDl9mwV3BVTWGxTdChJwLStM0vg,22326
timm/optim/lamb.py,sha256=OUqBRdTU3__STzREakLvF633wHt50dXAstCfFleyrlo,10586
timm/optim/laprop.py,sha256=y_v5ugcGlxbW9Qw0WAZ0vm7IGCUrwJjbp2DYbFzIFFA,5175
timm/optim/lars.py,sha256=obt_pS81oqIkP2Igj_HXxLUH9gUe7_kxsVrug7ylHRg,5166
timm/optim/lion.py,sha256=Flzdnn5CIiEQE_G6jIhO-G3wGAPnw5uwXdUTI3s5tB4,8902
timm/optim/lookahead.py,sha256=-fM1DEwFf_bpbNq2cXdkJyrobF4iaVNIBuman_RfRk8,2687
timm/optim/madgrad.py,sha256=u3Afclqrkh8FSnOpUe0HMGsuPXxD_ZJ5NcNtRSGZQuM,6951
timm/optim/mars.py,sha256=iqmLOIWS64k7KFivit63mEriAJIdz0imM7OlvYzaDPs,7404
timm/optim/nadam.py,sha256=RLOTbmsUvTjl78dn0H9LZaQzTmldfLUPlnsmV1EfflU,4114
timm/optim/nadamw.py,sha256=pqvgaoxS44hmPEzR1myQqcm39KO0vwrnUwkBfLhkhtY,15148
timm/optim/nvnovograd.py,sha256=TuQZtZDeT9VhgDHTTLzXEGx9PoILPh3GUEst9ysIxZQ,4953
timm/optim/optim_factory.py,sha256=qnZhen020_lr3FT7hg0EegsYxmAKMC0en_zuugtkEZw,429
timm/optim/radam.py,sha256=O0AUp6mgnqWZGzUvceMG1Y10oBb-M7MgXsDZKEswzFw,3843
timm/optim/rmsprop_tf.py,sha256=5EFsrA6UySRmCtNCYfl15s--q-I5uMR6PZ22d8xobGQ,7530
timm/optim/sgdp.py,sha256=6Vl_Gumv5NHsC-qnZZLNOPsBMGxfCaNvPtQ2aIg_EVA,2487
timm/optim/sgdw.py,sha256=vyORzbc-ieBIiqt7_iLqutEtw4YDk3FpV-P2HvCTaK4,11285
timm/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
timm/scheduler/__init__.py,sha256=O3yNRcgje9l_wUhxN5VteAARRGMpCxOmhbOmV_8j2Dw,330
timm/scheduler/__pycache__/__init__.cpython-312.pyc,,
timm/scheduler/__pycache__/cosine_lr.cpython-312.pyc,,
timm/scheduler/__pycache__/multistep_lr.cpython-312.pyc,,
timm/scheduler/__pycache__/plateau_lr.cpython-312.pyc,,
timm/scheduler/__pycache__/poly_lr.cpython-312.pyc,,
timm/scheduler/__pycache__/scheduler.cpython-312.pyc,,
timm/scheduler/__pycache__/scheduler_factory.cpython-312.pyc,,
timm/scheduler/__pycache__/step_lr.cpython-312.pyc,,
timm/scheduler/__pycache__/tanh_lr.cpython-312.pyc,,
timm/scheduler/cosine_lr.py,sha256=XydLwItrCF4NCCOnq39txCY6fCDzOTRDnizkkiZMpq4,3942
timm/scheduler/multistep_lr.py,sha256=m33HB6ToJQCOAfF7n0Ofab7tpzwlg6eB5EOIb_6v_Qc,1950
timm/scheduler/plateau_lr.py,sha256=6SmHEBIfgzE83GMHK_ZMIz-L0478HJ3ahwRgDZ9CJbk,3603
timm/scheduler/poly_lr.py,sha256=PSakDg4Upxs4ELeACCxhZN9ggziB6Ai-z2L6c2iWenQ,3773
timm/scheduler/scheduler.py,sha256=Dy3Os_z2qI9AA0InPN0oroOdETW7weYb1Uk7C0ZXurk,5426
timm/scheduler/scheduler_factory.py,sha256=53LISRz3mxkWjXlYqB_pz2uxHTO3Hyivqw27z_Is76E,6933
timm/scheduler/step_lr.py,sha256=rHliA2nLVUQsOInjUnQgeGtH09aupY4uOXmQ-_ouNXc,1777
timm/scheduler/tanh_lr.py,sha256=r55GRN-NL6sALTYIoX4uHnpMI7V6YhW3EGE2UtClJJk,3707
timm/utils/__init__.py,sha256=r-mgE7sAOYatFEYjgdXXKH0RjOLvQ5FH0rcxv8-gNR0,846
timm/utils/__pycache__/__init__.cpython-312.pyc,,
timm/utils/__pycache__/agc.cpython-312.pyc,,
timm/utils/__pycache__/attention_extract.cpython-312.pyc,,
timm/utils/__pycache__/checkpoint_saver.cpython-312.pyc,,
timm/utils/__pycache__/clip_grad.cpython-312.pyc,,
timm/utils/__pycache__/cuda.cpython-312.pyc,,
timm/utils/__pycache__/decay_batch.cpython-312.pyc,,
timm/utils/__pycache__/distributed.cpython-312.pyc,,
timm/utils/__pycache__/jit.cpython-312.pyc,,
timm/utils/__pycache__/log.cpython-312.pyc,,
timm/utils/__pycache__/metrics.cpython-312.pyc,,
timm/utils/__pycache__/misc.cpython-312.pyc,,
timm/utils/__pycache__/model.cpython-312.pyc,,
timm/utils/__pycache__/model_ema.cpython-312.pyc,,
timm/utils/__pycache__/onnx.cpython-312.pyc,,
timm/utils/__pycache__/random.cpython-312.pyc,,
timm/utils/__pycache__/summary.cpython-312.pyc,,
timm/utils/agc.py,sha256=6lZCChfbW0KGNMfkzztWD_NP87ESopjk24Xtb3WbBqU,1624
timm/utils/attention_extract.py,sha256=CCPMmnEk4dM1UrvOpY7sfGZI8KLQekJIT7rflg-4qDw,3226
timm/utils/checkpoint_saver.py,sha256=daqE3Wf4hJxFh1K-_-YTE4wnwHgb_9uIoQI_yk0jSR4,6894
timm/utils/clip_grad.py,sha256=iYFEf7fvPbpyh5K1SI-EKey5Gqs2gztR9VUUGja0GB0,796
timm/utils/cuda.py,sha256=F-P-C_bfBbI_LobcXLZVUn0SqZOHfoe9lefvdWadUbM,2173
timm/utils/decay_batch.py,sha256=5fOrMO985Pw8uzvBK78RwYCoH3Nv2jb46OGa9GkJ6LA,1762
timm/utils/distributed.py,sha256=DUFmxhKyHG1ecCoAKd1MfQGupAuEqn2STrmwq8w1YdQ,5918
timm/utils/jit.py,sha256=E6qGLHd9ja7CyJ8dAYM1tMP0mm5Db8Y8IzOTdI54-eY,2202
timm/utils/log.py,sha256=BdZ2OqWo3v8d7wsDRJ-uACcoeNUhS8TJSwI3CYvq3Ss,1015
timm/utils/metrics.py,sha256=RSHpbbkyW6FsbxT6TzcBL7MZh4sv4A_GG1Bo8aN5qKc,901
timm/utils/misc.py,sha256=wh1RUZPEyVOtA3HFkbunNpAKPbrOKQYeJqhvODmSyyQ,1105
timm/utils/model.py,sha256=y9CuB5wxu975wh5uq-A4q0QGYbv200G2Wtop_plCWk0,10577
timm/utils/model_ema.py,sha256=-O-HAZKLxo069T_fN3CceNEaxGjJvbCxIFWjxWHJ0SM,11244
timm/utils/onnx.py,sha256=DZqR39krviCG7s3slYJQ9ERWkgtLCUGkAraq8eOX_4s,3710
timm/utils/random.py,sha256=Ysv6F3nIO8JYE8j6UrDxGyJDp3uNpq5v8U0KqL_8dic,178
timm/utils/summary.py,sha256=HYD5nJsTOD3DGqCPUu2L3sX4VjNFTybHbjwntSEWBi4,1325
timm/version.py,sha256=zLcjbm-Ouif3LdoxWWW5X6rfr_u-CfM4mnh2IeVAOb0,23
