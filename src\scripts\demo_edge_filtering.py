"""
Demonstration of Edge Artifact Filtering in Oil Particle Detection
Shows before/after comparison of detection results
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# Add models to path
sys.path.append('models')
sys.path.append('utils')

from transformer_detector import TransformerParticleDetector
from particle_analyzer import ParticleAnalyzer
from edge_artifact_filter import EdgeArtifactFilter

def create_demo_image():
    """Create a demonstration image with particles and edge artifacts"""
    
    # Create base microscopy image
    height, width = 480, 640
    image = np.ones((height, width, 3), dtype=np.uint8) * 160  # Gray background
    
    # Add realistic oil particles in center area
    particles = [
        # (x, y, w, h, particle_type)
        (200, 150, 35, 30, 'normal'),      # Normal particle
        (350, 200, 45, 25, 'cutting'),     # Cutting wear
        (150, 300, 40, 40, 'spherical'),   # Spherical particle
        (400, 120, 60, 20, 'sliding'),     # Sliding wear
        (280, 350, 30, 35, 'lamellar'),    # Lamellar particle
        (500, 280, 25, 25, 'normal'),      # Another normal
    ]
    
    # Draw particles with realistic appearance
    for x, y, w, h, ptype in particles:
        if ptype == 'cutting':
            # Sharp, angular cutting particle
            pts = np.array([[x, y+h//2], [x+w//3, y], [x+w, y+h//3], 
                           [x+2*w//3, y+h], [x, y+2*h//3]], np.int32)
            cv2.fillPoly(image, [pts], (45, 45, 45))
        elif ptype == 'spherical':
            # Round spherical particle
            cv2.circle(image, (x+w//2, y+h//2), min(w,h)//2, (50, 50, 50), -1)
        elif ptype == 'sliding':
            # Elongated sliding particle
            cv2.ellipse(image, (x+w//2, y+h//2), (w//2, h//4), 0, 0, 360, (40, 40, 40), -1)
        else:
            # Normal/lamellar particles
            cv2.ellipse(image, (x+w//2, y+h//2), (w//2, h//2), 0, 0, 360, (55, 55, 55), -1)
        
        # Add some texture/detail
        cv2.ellipse(image, (x+w//2, y+h//2), (w//4, h//4), 0, 0, 360, (35, 35, 35), -1)
    
    # Add edge artifacts (the problem we're solving)
    # Top edge shadow
    image[:25, :] = [8, 8, 8]
    
    # Bottom edge shadow  
    image[-30:, :] = [12, 12, 12]
    
    # Left edge shadow
    image[:, :35] = [10, 10, 10]
    
    # Right edge shadow
    image[:, -40:] = [6, 6, 6]
    
    # Corner artifacts (very dark)
    image[:50, :50] = [3, 3, 3]        # Top-left
    image[:50, -50:] = [3, 3, 3]       # Top-right
    image[-50:, :50] = [3, 3, 3]       # Bottom-left
    image[-50:, -50:] = [3, 3, 3]      # Bottom-right
    
    # Add some noise in edge areas (simulating imaging artifacts)
    noise_areas = [
        (0, 0, 35, height),           # Left edge
        (width-40, 0, 40, height),    # Right edge
        (0, 0, width, 25),            # Top edge
        (0, height-30, width, 30)     # Bottom edge
    ]
    
    for x, y, w, h in noise_areas:
        noise = np.random.randint(0, 20, (h, w, 3))
        image[y:y+h, x:x+w] = np.clip(image[y:y+h, x:x+w].astype(int) + noise, 0, 255).astype(np.uint8)
    
    return image

def demonstrate_edge_filtering():
    """Demonstrate edge artifact filtering with oil particle detection"""
    
    print("🔬 OIL PARTICLE DETECTION - EDGE ARTIFACT FILTERING DEMO")
    print("="*60)
    
    # Create demonstration image
    print("📸 Creating demonstration image with edge artifacts...")
    demo_image = create_demo_image()
    
    # Save demo image
    cv2.imwrite('demo_image_with_artifacts.jpg', demo_image)
    print("   Demo image saved: demo_image_with_artifacts.jpg")
    
    # Initialize detectors
    print("\n🤖 Initializing detection models...")
    
    # Detector WITHOUT edge filtering
    print("   - Transformer detector (no filtering)")
    detector_no_filter = TransformerParticleDetector(
        confidence_threshold=0.5,
        filter_edge_artifacts=False  # Disabled
    )
    
    # Detector WITH edge filtering  
    print("   - Transformer detector (with edge filtering)")
    detector_with_filter = TransformerParticleDetector(
        confidence_threshold=0.5,
        filter_edge_artifacts=True   # Enabled
    )
    
    # Analyzer WITHOUT edge filtering
    print("   - Particle analyzer (no filtering)")
    analyzer_no_filter = ParticleAnalyzer(filter_edge_artifacts=False)
    
    # Analyzer WITH edge filtering
    print("   - Particle analyzer (with edge filtering)")
    analyzer_with_filter = ParticleAnalyzer(filter_edge_artifacts=True)
    
    # Run detections
    print("\n🔍 Running particle detection...")
    
    print("   - Detection without edge filtering...")
    detections_no_filter = detector_no_filter.detect_particles(demo_image)
    
    print("   - Detection with edge filtering...")
    detections_with_filter = detector_with_filter.detect_particles(demo_image)
    
    # Run analysis
    print("\n📊 Running particle analysis...")
    
    print("   - Analysis without edge filtering...")
    analysis_no_filter = analyzer_no_filter.analyze_particles(demo_image, detections_no_filter)
    
    print("   - Analysis with edge filtering...")
    analysis_with_filter = analyzer_with_filter.analyze_particles(demo_image, detections_with_filter)
    
    # Compare results
    print("\n📈 COMPARISON RESULTS")
    print("-"*40)
    print(f"Without filtering: {len(detections_no_filter)} detections")
    print(f"With filtering:    {len(detections_with_filter)} detections")
    print(f"Filtered out:      {len(detections_no_filter) - len(detections_with_filter)} artifacts")
    
    # Visualize results
    print("\n🎨 Creating visualization...")
    visualize_comparison(demo_image, detections_no_filter, detections_with_filter)
    
    # Show edge mask
    print("\n🎭 Showing edge artifact mask...")
    edge_filter = EdgeArtifactFilter()
    edge_filter.visualize_filtering(demo_image, save_path='demo_edge_mask.png')
    
    return {
        'without_filtering': len(detections_no_filter),
        'with_filtering': len(detections_with_filter),
        'artifacts_removed': len(detections_no_filter) - len(detections_with_filter)
    }

def visualize_comparison(image, detections_no_filter, detections_with_filter):
    """Create side-by-side comparison visualization"""
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Original image
    axes[0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Original Image\n(with edge artifacts)', fontsize=12)
    axes[0].axis('off')
    
    # Without filtering
    axes[1].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    for i, det in enumerate(detections_no_filter):
        if 'bbox' in det:
            x, y, w, h = det['bbox']
            # Red boxes for all detections (including artifacts)
            rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='red', facecolor='none')
            axes[1].add_patch(rect)
            axes[1].text(x, y-5, f"{i+1}", color='red', fontsize=10, weight='bold')
    axes[1].set_title(f'Without Edge Filtering\n{len(detections_no_filter)} detections (includes artifacts)', fontsize=12)
    axes[1].axis('off')
    
    # With filtering
    axes[2].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    for i, det in enumerate(detections_with_filter):
        if 'bbox' in det:
            x, y, w, h = det['bbox']
            # Green boxes for valid detections only
            rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='green', facecolor='none')
            axes[2].add_patch(rect)
            axes[2].text(x, y-5, f"{i+1}", color='green', fontsize=10, weight='bold')
    axes[2].set_title(f'With Edge Filtering\n{len(detections_with_filter)} valid detections', fontsize=12)
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig('edge_filtering_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Comparison visualization saved: edge_filtering_comparison.png")

def main():
    """Main demonstration function"""
    
    print("🚀 STARTING EDGE ARTIFACT FILTERING DEMONSTRATION")
    print()
    print("This demo shows how edge artifact filtering improves")
    print("oil particle detection by removing false detections")
    print("from black edges and shadows in microscopy images.")
    print()
    
    try:
        # Run demonstration
        results = demonstrate_edge_filtering()
        
        # Summary
        print("\n🎯 DEMONSTRATION SUMMARY")
        print("="*40)
        print(f"✅ Successfully filtered out {results['artifacts_removed']} edge artifacts")
        print(f"📊 Detection accuracy improved by removing false positives")
        print(f"🔬 Valid particles: {results['with_filtering']}")
        print(f"🚫 Artifacts removed: {results['artifacts_removed']}")
        
        improvement = (results['artifacts_removed'] / results['without_filtering']) * 100
        print(f"📈 Filtering efficiency: {improvement:.1f}%")
        
        print("\n📁 Generated files:")
        print("   - demo_image_with_artifacts.jpg")
        print("   - edge_filtering_comparison.png") 
        print("   - demo_edge_mask.png")
        
        print("\n✅ Edge artifact filtering demonstration completed!")
        print("   Your GUI now automatically filters out black edges and shadows!")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        print("   The filtering is still enabled in your GUI")
    
    return results

if __name__ == '__main__':
    main()
