"""
Advanced Augmentation Pipeline for Oil Particle Detection
Specialized transformations for improving detection of small particles
"""

import cv2
import numpy as np
import albumentations as A
from albumentations.pytorch import ToTensorV2
import random
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ParticleSpecificAugmentation(A.ImageOnlyTransform):
    """
    Custom augmentation specifically designed for particle detection
    Simulates various oil conditions and particle characteristics
    """
    
    def __init__(self, 
                 noise_intensity: float = 0.1,
                 particle_enhancement: float = 0.2,
                 oil_simulation: bool = True,
                 always_apply: bool = False, 
                 p: float = 0.5):
        super().__init__(always_apply, p)
        self.noise_intensity = noise_intensity
        self.particle_enhancement = particle_enhancement
        self.oil_simulation = oil_simulation
    
    def apply(self, img: np.ndarray, **params) -> np.ndarray:
        """Apply particle-specific augmentations"""
        img = img.copy()
        
        # Enhance particle contrast
        if random.random() < 0.5:
            img = self._enhance_particles(img)
        
        # Simulate oil medium effects
        if self.oil_simulation and random.random() < 0.3:
            img = self._simulate_oil_medium(img)
        
        # Add realistic noise
        if random.random() < 0.4:
            img = self._add_particle_noise(img)
        
        return img
    
    def _enhance_particles(self, img: np.ndarray) -> np.ndarray:
        """Enhance particle visibility through contrast adjustment"""
        # Convert to LAB color space for better contrast control
        lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge and convert back
        lab = cv2.merge([l, a, b])
        img = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        
        return img
    
    def _simulate_oil_medium(self, img: np.ndarray) -> np.ndarray:
        """Simulate oil medium effects like refraction and scattering"""
        # Add slight blur to simulate oil medium
        kernel_size = random.choice([3, 5])
        img = cv2.GaussianBlur(img, (kernel_size, kernel_size), 0.5)
        
        # Adjust color temperature to simulate oil tint
        img = img.astype(np.float32)
        img[:, :, 0] *= random.uniform(0.9, 1.1)  # Red channel
        img[:, :, 1] *= random.uniform(0.95, 1.05)  # Green channel
        img[:, :, 2] *= random.uniform(0.8, 1.0)   # Blue channel
        img = np.clip(img, 0, 255).astype(np.uint8)
        
        return img
    
    def _add_particle_noise(self, img: np.ndarray) -> np.ndarray:
        """Add realistic particle-like noise"""
        noise = np.random.normal(0, self.noise_intensity * 255, img.shape)
        img = img.astype(np.float32) + noise
        img = np.clip(img, 0, 255).astype(np.uint8)
        return img


class SyntheticParticleGeneration(A.ImageOnlyTransform):
    """
    Generate synthetic particles to augment training data
    Adds realistic particle-like objects to images
    """
    
    def __init__(self, 
                 max_particles: int = 5,
                 particle_size_range: Tuple[int, int] = (2, 15),
                 always_apply: bool = False, 
                 p: float = 0.2):
        super().__init__(always_apply, p)
        self.max_particles = max_particles
        self.particle_size_range = particle_size_range
    
    def apply(self, img: np.ndarray, **params) -> np.ndarray:
        """Add synthetic particles to the image"""
        img = img.copy()
        
        num_particles = random.randint(1, self.max_particles)
        
        for _ in range(num_particles):
            img = self._add_synthetic_particle(img)
        
        return img
    
    def _add_synthetic_particle(self, img: np.ndarray) -> np.ndarray:
        """Add a single synthetic particle"""
        h, w = img.shape[:2]
        
        # Random position
        x = random.randint(0, w - 1)
        y = random.randint(0, h - 1)
        
        # Random size
        size = random.randint(*self.particle_size_range)
        
        # Random particle type
        particle_type = random.choice(['spherical', 'irregular', 'elongated'])
        
        if particle_type == 'spherical':
            cv2.circle(img, (x, y), size // 2, self._get_particle_color(), -1)
        elif particle_type == 'irregular':
            # Create irregular shape using random points
            points = self._generate_irregular_shape(x, y, size)
            cv2.fillPoly(img, [points], self._get_particle_color())
        else:  # elongated
            # Create elongated particle
            angle = random.uniform(0, 180)
            axes = (size, size // 3)
            cv2.ellipse(img, (x, y), axes, angle, 0, 360, self._get_particle_color(), -1)
        
        return img
    
    def _generate_irregular_shape(self, center_x: int, center_y: int, size: int) -> np.ndarray:
        """Generate points for an irregular particle shape"""
        num_points = random.randint(6, 10)
        points = []
        
        for i in range(num_points):
            angle = (2 * np.pi * i) / num_points
            radius = size // 2 + random.randint(-size // 4, size // 4)
            x = int(center_x + radius * np.cos(angle))
            y = int(center_y + radius * np.sin(angle))
            points.append([x, y])
        
        return np.array(points, dtype=np.int32)
    
    def _get_particle_color(self) -> Tuple[int, int, int]:
        """Get realistic particle color"""
        # Particles are typically darker than background
        base_intensity = random.randint(50, 150)
        return (
            base_intensity + random.randint(-20, 20),
            base_intensity + random.randint(-20, 20),
            base_intensity + random.randint(-20, 20)
        )


class BackgroundSubstitution(A.ImageOnlyTransform):
    """
    Substitute background while preserving particles
    Simulates different oil conditions and lighting
    """
    
    def __init__(self, 
                 background_types: List[str] = None,
                 always_apply: bool = False, 
                 p: float = 0.3):
        super().__init__(always_apply, p)
        self.background_types = background_types or ['clean_oil', 'dirty_oil', 'aged_oil']
    
    def apply(self, img: np.ndarray, **params) -> np.ndarray:
        """Apply background substitution"""
        img = img.copy()
        
        background_type = random.choice(self.background_types)
        
        if background_type == 'clean_oil':
            img = self._apply_clean_oil_background(img)
        elif background_type == 'dirty_oil':
            img = self._apply_dirty_oil_background(img)
        else:  # aged_oil
            img = self._apply_aged_oil_background(img)
        
        return img
    
    def _apply_clean_oil_background(self, img: np.ndarray) -> np.ndarray:
        """Apply clean oil background effect"""
        # Slightly brighten the background
        img = img.astype(np.float32)
        img += random.uniform(5, 15)
        img = np.clip(img, 0, 255).astype(np.uint8)
        return img
    
    def _apply_dirty_oil_background(self, img: np.ndarray) -> np.ndarray:
        """Apply dirty oil background effect"""
        # Add brownish tint and reduce brightness
        img = img.astype(np.float32)
        img[:, :, 0] *= random.uniform(0.8, 0.9)   # Red
        img[:, :, 1] *= random.uniform(0.7, 0.8)   # Green
        img[:, :, 2] *= random.uniform(0.6, 0.7)   # Blue
        img = np.clip(img, 0, 255).astype(np.uint8)
        return img
    
    def _apply_aged_oil_background(self, img: np.ndarray) -> np.ndarray:
        """Apply aged oil background effect"""
        # Add yellowish tint and increase contrast
        img = img.astype(np.float32)
        img[:, :, 0] *= random.uniform(1.0, 1.1)   # Red
        img[:, :, 1] *= random.uniform(1.0, 1.1)   # Green
        img[:, :, 2] *= random.uniform(0.8, 0.9)   # Blue
        img = np.clip(img, 0, 255).astype(np.uint8)
        return img


def get_advanced_particle_augmentations(image_size: int = 800, 
                                      training: bool = True) -> A.Compose:
    """
    Get advanced augmentation pipeline for particle detection
    
    Args:
        image_size: Target image size
        training: Whether this is for training (more aggressive) or validation
        
    Returns:
        Albumentations composition
    """
    
    if training:
        # Training augmentations - more aggressive
        transforms = [
            # Geometric transformations
            A.RandomRotate90(p=0.5),
            A.HorizontalFlip(p=0.5),
            A.RandomScale(scale_limit=0.3, p=0.6),
            A.ShiftScaleRotate(
                shift_limit=0.1,
                scale_limit=0.2,
                rotate_limit=30,
                border_mode=cv2.BORDER_REFLECT,
                p=0.7
            ),
            A.ElasticTransform(
                alpha=1,
                sigma=50,
                border_mode=cv2.BORDER_REFLECT,
                p=0.3
            ),
            A.Perspective(scale=(0.05, 0.1), p=0.3),
            
            # Photometric transformations
            A.RandomBrightnessContrast(
                brightness_limit=0.3,
                contrast_limit=0.3,
                p=0.7
            ),
            A.HueSaturationValue(
                hue_shift_limit=15,
                sat_shift_limit=30,
                val_shift_limit=20,
                p=0.5
            ),
            A.CLAHE(clip_limit=4.0, tile_grid_size=(8, 8), p=0.5),
            A.RandomGamma(gamma_limit=(80, 120), p=0.4),
            
            # Noise and blur
            A.OneOf([
                A.GaussNoise(noise_scale_factor=0.1),
                A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5)),
                A.MultiplicativeNoise(multiplier=[0.9, 1.1], per_channel=True),
            ], p=0.4),
            
            A.OneOf([
                A.MotionBlur(blur_limit=5),
                A.GaussianBlur(blur_limit=3),
                A.Defocus(radius=(1, 3), alias_blur=(0.1, 0.5)),
            ], p=0.3),
            
            # Weather and environmental effects
            A.RandomFog(fog_coef=0.2, p=0.1),
            A.RandomSunFlare(
                flare_roi=(0, 0, 1, 0.5),
                angle=0.5,
                p=0.05
            ),
            
            # Particle-specific augmentations
            ParticleSpecificAugmentation(p=0.4),
            SyntheticParticleGeneration(p=0.2),
            BackgroundSubstitution(p=0.3),
            
            # Final processing
            A.Resize(height=image_size, width=image_size),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ]
    else:
        # Validation augmentations - minimal
        transforms = [
            A.Resize(height=image_size, width=image_size),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ]
    
    return A.Compose(
        transforms,
        bbox_params=A.BboxParams(
            format='coco',
            label_fields=['class_labels'],
            min_visibility=0.3
        )
    )


def get_test_time_augmentations() -> List[A.Compose]:
    """
    Get test-time augmentation transforms for ensemble prediction
    
    Returns:
        List of augmentation compositions for TTA
    """
    
    base_transforms = [
        A.Resize(height=800, width=800),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ]
    
    tta_transforms = [
        # Original
        A.Compose(base_transforms),
        
        # Horizontal flip
        A.Compose([A.HorizontalFlip(p=1.0)] + base_transforms),
        
        # Vertical flip
        A.Compose([A.VerticalFlip(p=1.0)] + base_transforms),
        
        # Rotation
        A.Compose([A.Rotate(limit=15, p=1.0)] + base_transforms),
        
        # Scale
        A.Compose([A.RandomScale(scale_limit=0.1, p=1.0)] + base_transforms),
        
        # Brightness
        A.Compose([A.RandomBrightnessContrast(brightness_limit=0.1, contrast_limit=0, p=1.0)] + base_transforms),
    ]
    
    return tta_transforms
