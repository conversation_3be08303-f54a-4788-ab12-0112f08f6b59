@echo off
echo Oil Particle Detection System - System Installer
echo =================================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if errorlevel 1 (
    echo This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

set "INSTALL_DIR=%PROGRAMFILES%\Oil Particle Detector"
set "DESKTOP_SHORTCUT=%PUBLIC%\Desktop\Oil Particle Detector.lnk"

echo Installing to: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy all files
echo Copying application files...
xcopy /E /I /Y "*" "%INSTALL_DIR%\" >nul
if errorlevel 1 (
    echo [ERROR] Failed to copy files
    pause
    exit /b 1
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Oil Particle Detector.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Oil Particle Detection System'; $Shortcut.Save()"

REM Create start menu entry
set "START_MENU=%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Oil Particle Detector.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Oil Particle Detector.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Oil Particle Detection System'; $Shortcut.Save()"

echo.
echo [SUCCESS] Installation completed successfully!
echo.
echo Desktop shortcut: Oil Particle Detector
echo Start menu entry: Oil Particle Detector
echo Installation location: %INSTALL_DIR%
echo.
echo You can now run the application from:
echo - Desktop shortcut
echo - Start menu
echo - Or navigate to the installation folder
echo.
pause
