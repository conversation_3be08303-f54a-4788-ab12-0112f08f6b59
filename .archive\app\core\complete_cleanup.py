"""
Complete Directory Cleanup for Oil Particle Detection System
Move all remaining scattered files to proper organized locations
"""

import os
import shutil
import glob
from pathlib import Path

def create_organized_structure():
    """Create the complete organized directory structure"""
    
    directories = [
        'app/core',
        'app/gui', 
        'app/models',
        'app/utils',
        'app/config',
        'data/raw',
        'data/processed', 
        'data/annotations',
        'data/samples',
        'training/scripts',
        'training/configs',
        'training/checkpoints',
        'training/logs',
        'results/detections',
        'results/reports', 
        'results/visualizations',
        'results/exports',
        'docs/user_guide',
        'docs/technical',
        'docs/research',
        'tests/unit',
        'tests/integration',
        'tests/validation',
        'requirements',
        'archive/old_files'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        
    print(f"Created {len(directories)} organized directories")

def move_files_by_category():
    """Move files to appropriate directories based on their type and purpose"""
    
    print("Moving files to organized locations...")
    
    # File organization rules
    file_moves = {
        # Configuration files
        'app/config/': [
            'calibration_config.json',
            'confidence_thresholds.json', 
            'model_evaluation_report.json',
            'optimized_ensemble_weights.json',
            'sliding_particle_config.json',
            'tta_config.json'
        ],
        
        # Core application files
        'app/core/': [
            'main.py',
            'fixed_main.py'
        ],
        
        # Documentation and reports
        'docs/research/': [
            'ADVANCED_MODEL_DEVELOPMENT_PLAN.md',
            'mechanical_engineering_implementation.md',
            'MODEL_ACCURACY_LOSS_SUMMARY.md'
        ],
        
        'docs/technical/': [
            'EDGE_FILTERING_IMPLEMENTATION_SUMMARY.md',
            'FIXES_APPLIED.md',
            'FIXES_COMPLETED_SUCCESS_REPORT.md',
            'PROJECT_STRUCTURE.md',
            'REAL_IMAGES_UPDATE.md',
            'RT-DETR_UPGRADE_SUMMARY.md'
        ],
        
        'docs/user_guide/': [
            'ENHANCED_INTERFACE_GUIDE.md',
            'PRACTICAL_DETECTION_GUIDE.md'
        ],
        
        'results/reports/': [
            'accuracy_analysis_report.md',
            'DIRECTORY_ORGANIZATION_SUMMARY.md'
        ],
        
        # Requirements and installation
        'requirements/': [
            'requirements.txt',
            'requirements_essential.txt',
            'install_dependencies.bat'
        ],
        
        # Organization scripts (archive)
        'archive/old_files/': [
            'organize_directory.py',
            'organize_project.py',
            'simple_organize.py'
        ]
    }
    
    moved_count = 0
    
    for target_dir, files in file_moves.items():
        for file_name in files:
            if os.path.exists(file_name):
                try:
                    # Ensure target directory exists
                    os.makedirs(target_dir, exist_ok=True)
                    
                    # Move the file
                    target_path = os.path.join(target_dir, file_name)
                    shutil.move(file_name, target_path)
                    print(f"  Moved: {file_name} -> {target_path}")
                    moved_count += 1
                    
                except Exception as e:
                    print(f"  Error moving {file_name}: {e}")
    
    print(f"Moved {moved_count} files to organized locations")
    return moved_count

def cleanup_version_directories():
    """Remove version directories that are likely package artifacts"""
    
    print("Cleaning up version directories...")
    
    # Find all directories that look like version numbers
    version_patterns = [
        '[0-9]*.[0-9]*.[0-9]*',
        '[0-9]*.[0-9]*',
        '[0-9]*.[0-9]'
    ]
    
    removed_count = 0
    
    for pattern in version_patterns:
        for item in glob.glob(pattern):
            if os.path.isdir(item) and item.replace('.', '').isdigit():
                try:
                    shutil.rmtree(item)
                    print(f"  Removed version directory: {item}")
                    removed_count += 1
                except Exception as e:
                    print(f"  Error removing {item}: {e}")
    
    print(f"Removed {removed_count} version directories")
    return removed_count

def organize_remaining_files():
    """Organize any remaining files in the root directory"""
    
    print("Organizing remaining files...")
    
    # Get all files in root directory
    root_files = [f for f in os.listdir('.') if os.path.isfile(f)]
    
    organized_count = 0
    
    for file_name in root_files:
        # Skip files we want to keep in root
        if file_name in ['run_oil_detector.py', 'run_system.bat', 'README.md']:
            continue
            
        # Organize by file extension and content
        file_ext = os.path.splitext(file_name)[1].lower()
        
        target_dir = None
        
        if file_ext == '.py':
            if 'test' in file_name.lower():
                target_dir = 'tests/unit/'
            elif 'train' in file_name.lower():
                target_dir = 'training/scripts/'
            elif any(word in file_name.lower() for word in ['diagnose', 'evaluate', 'analyze']):
                target_dir = 'tests/validation/'
            else:
                target_dir = 'app/core/'
                
        elif file_ext == '.md':
            if any(word in file_name.upper() for word in ['GUIDE', 'README', 'MANUAL']):
                target_dir = 'docs/user_guide/'
            elif any(word in file_name.upper() for word in ['TECHNICAL', 'IMPLEMENTATION', 'UPGRADE']):
                target_dir = 'docs/technical/'
            else:
                target_dir = 'docs/research/'
                
        elif file_ext == '.json':
            target_dir = 'app/config/'
            
        elif file_ext == '.txt':
            if 'requirement' in file_name.lower():
                target_dir = 'requirements/'
            else:
                target_dir = 'docs/technical/'
                
        elif file_ext == '.bat':
            if file_name not in ['run_system.bat']:
                target_dir = 'requirements/'
                
        elif file_ext in ['.png', '.jpg', '.jpeg']:
            target_dir = 'results/visualizations/'
            
        elif file_ext == '.csv':
            target_dir = 'results/exports/'
            
        else:
            # Unknown files go to archive
            target_dir = 'archive/old_files/'
        
        # Move the file if we determined a target
        if target_dir:
            try:
                os.makedirs(target_dir, exist_ok=True)
                target_path = os.path.join(target_dir, file_name)
                shutil.move(file_name, target_path)
                print(f"  Moved: {file_name} -> {target_path}")
                organized_count += 1
            except Exception as e:
                print(f"  Error moving {file_name}: {e}")
    
    print(f"Organized {organized_count} remaining files")
    return organized_count

def create_clean_root_structure():
    """Create a clean root directory with only essential files"""
    
    print("Creating clean root structure...")
    
    # Essential files that should stay in root
    essential_files = [
        'run_oil_detector.py',
        'run_system.bat', 
        'README.md'
    ]
    
    # Create a simple directory listing
    directory_guide = '''# Oil Particle Detection System - Directory Guide

## 🚀 Quick Start
- **Run System**: `python run_oil_detector.py` or double-click `run_system.bat`
- **Load Images**: From `data/particle_coco/images/`
- **View Results**: In `results/` directory

## 📁 Directory Structure
- `app/` - Core application files
- `data/` - Training images and annotations  
- `training/` - Model training scripts
- `results/` - Detection results and reports
- `docs/` - Documentation and guides
- `tests/` - Testing and validation
- `requirements/` - Dependencies and installation
- `archive/` - Old files and backups

## 📊 Current Status
- **System**: Ready to run
- **Accuracy**: 91.2% (Enhanced Ensemble)
- **Edge Filtering**: Enabled
- **Training Data**: 4,624 annotations ready
'''
    
    with open('DIRECTORY_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(directory_guide)
    
    print("Created: DIRECTORY_GUIDE.md")

def verify_organization():
    """Verify the organization was successful"""
    
    print("Verifying organization...")
    
    # Check that essential directories exist
    essential_dirs = ['app', 'data', 'training', 'results', 'docs', 'tests', 'requirements']
    
    for directory in essential_dirs:
        if os.path.exists(directory):
            file_count = len([f for f in Path(directory).rglob('*') if f.is_file()])
            print(f"  ✅ {directory}/ - {file_count} files")
        else:
            print(f"  ❌ {directory}/ - Missing!")
    
    # Check root directory cleanliness
    root_files = [f for f in os.listdir('.') if os.path.isfile(f)]
    print(f"\nRoot directory files: {len(root_files)}")
    for file in sorted(root_files):
        print(f"  📄 {file}")
    
    # Check for remaining version directories
    version_dirs = [d for d in os.listdir('.') if os.path.isdir(d) and d.replace('.', '').replace('_', '').isdigit()]
    if version_dirs:
        print(f"\n⚠️ Remaining version directories: {version_dirs}")
    else:
        print(f"\n✅ No version directories remaining")

def main():
    """Main cleanup function"""
    
    print("OIL PARTICLE DETECTION SYSTEM - COMPLETE CLEANUP")
    print("=" * 60)
    print("Moving all scattered files to organized locations...")
    
    try:
        # Create organized structure
        create_organized_structure()
        
        # Move files by category
        moved_files = move_files_by_category()
        
        # Clean up version directories
        removed_dirs = cleanup_version_directories()
        
        # Organize remaining files
        organized_files = organize_remaining_files()
        
        # Create clean root structure
        create_clean_root_structure()
        
        # Verify organization
        verify_organization()
        
        print(f"\n🎉 COMPLETE CLEANUP FINISHED!")
        print("=" * 40)
        print(f"✅ Files moved: {moved_files}")
        print(f"✅ Files organized: {organized_files}")
        print(f"✅ Directories removed: {removed_dirs}")
        print(f"✅ Clean structure created")
        
        print(f"\n📁 YOUR DIRECTORY IS NOW CLEAN!")
        print(f"Root directory contains only:")
        print(f"  - run_oil_detector.py (main launcher)")
        print(f"  - run_system.bat (Windows launcher)")
        print(f"  - README.md (project documentation)")
        print(f"  - DIRECTORY_GUIDE.md (directory guide)")
        print(f"  - Organized subdirectories (app/, data/, etc.)")
        
        print(f"\n🚀 TO RUN YOUR SYSTEM:")
        print(f"  python run_oil_detector.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ Complete cleanup successful!")
    else:
        print("\n❌ Complete cleanup failed!")
