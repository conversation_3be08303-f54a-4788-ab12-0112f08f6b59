#!/usr/bin/env python3
"""
Build script to create Oil Particle Detector.exe
Creates a standalone executable with all dependencies included
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def install_pyinstaller():
    """Install PyInstaller for building executable"""
    print("Installing PyInstaller...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "pyinstaller"
        ], check=True)
        print("✅ PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyInstaller: {e}")
        return False

def create_spec_file():
    """Create PyInstaller spec file for the application"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launch_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('oil_detector', 'oil_detector'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'cv2',
        'numpy',
        'matplotlib',
        'scipy',
        'PIL',
        'oil_detector.gui.modern_interface',
    ],
    hookspath=[],
    hooksconfig={
        'PyQt5': {
            'qt_plugins': ['platforms', 'imageformats']
        }
    },
    runtime_hooks=[],
    excludes=['torch', 'torchvision', 'transformers', 'timm'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Oil Particle Detector',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)
'''

    with open('oil_particle_detector.spec', 'w') as f:
        f.write(spec_content)

    print("✅ Created PyInstaller spec file")

def create_icon():
    """Create application icon"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple icon
        size = (256, 256)
        img = Image.new('RGBA', size, (70, 130, 180, 255))  # Steel blue
        draw = ImageDraw.Draw(img)
        
        # Draw a simple particle detection icon
        # Outer circle
        draw.ellipse([50, 50, 206, 206], outline=(255, 255, 255, 255), width=8)
        
        # Inner particles
        particles = [
            (80, 80, 100, 100),
            (120, 120, 140, 140),
            (160, 90, 180, 110),
            (90, 160, 110, 180),
            (150, 150, 170, 170)
        ]
        
        for particle in particles:
            draw.ellipse(particle, fill=(255, 255, 0, 255))  # Yellow particles
        
        # Save as ICO
        img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Created application icon")
        return True
        
    except ImportError:
        print("⚠️  PIL not available, using default icon")
        return False
    except Exception as e:
        print(f"⚠️  Could not create icon: {e}")
        return False

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    try:
        # Use the virtual environment's PyInstaller
        venv_pyinstaller = Path(".venv/Scripts/pyinstaller.exe")
        if venv_pyinstaller.exists():
            cmd = [str(venv_pyinstaller)]
        else:
            cmd = [sys.executable, "-m", "PyInstaller"]

        # Use direct command line approach to avoid Qt plugin issues
        cmd.extend([
            "--onefile",
            "--windowed",
            "--name", "Oil Particle Detector",
            "--distpath", "dist",
            "--workpath", "build",
            "--add-data", "oil_detector;oil_detector",
            "--hidden-import", "PyQt5.QtCore",
            "--hidden-import", "PyQt5.QtGui",
            "--hidden-import", "PyQt5.QtWidgets",
            "--hidden-import", "oil_detector.gui.modern_interface",
            "--exclude-module", "torch",
            "--exclude-module", "torchvision",
            "--exclude-module", "transformers",
            "--exclude-module", "timm",
            "--icon", "icon.ico",
            "launch_app.py"
        ])

        subprocess.run(cmd, check=True)
        print("✅ Executable built successfully")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build executable: {e}")
        return False

def create_installer_script():
    """Create a simple installer script"""
    installer_content = '''@echo off
echo Oil Particle Detection System - Installer
echo ==========================================
echo.

set "INSTALL_DIR=%PROGRAMFILES%\\Oil Particle Detector"
set "DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\Oil Particle Detector.lnk"

echo Installing to: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy executable
copy "Oil Particle Detector.exe" "%INSTALL_DIR%\\" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable
    pause
    exit /b 1
)

REM Create desktop shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Oil Particle Detector.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Oil Particle Detection System'; $Shortcut.Save()"

echo.
echo ✅ Installation completed successfully!
echo.
echo Desktop shortcut created: Oil Particle Detector
echo Installation location: %INSTALL_DIR%
echo.
pause
'''
    
    with open('install.bat', 'w') as f:
        f.write(installer_content)
    
    print("✅ Created installer script")

def main():
    """Main build process"""
    print("Oil Particle Detection System - Executable Builder")
    print("=" * 55)
    
    # Check if we're in the right directory
    if not Path("oil_detector").exists():
        print("❌ Error: oil_detector package not found")
        print("Please run this script from the project root directory")
        return False
    
    # Install PyInstaller
    if not install_pyinstaller():
        return False
    
    # Create icon
    create_icon()
    
    # Skip spec file creation - using direct command line
    
    # Build executable
    if not build_executable():
        return False
    
    # Create installer
    create_installer_script()
    
    # Check if executable was created
    exe_path = Path("dist/Oil Particle Detector.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"")
        print("🎉 SUCCESS! Executable created successfully!")
        print("=" * 55)
        print(f"📁 Location: {exe_path.absolute()}")
        print(f"📊 Size: {size_mb:.1f} MB")
        print(f"🚀 Installer: install.bat")
        print("")
        print("To distribute:")
        print("1. Copy 'Oil Particle Detector.exe' to target machine")
        print("2. Run 'install.bat' as administrator for system-wide install")
        print("3. Or run the .exe directly for portable use")
        return True
    else:
        print("❌ Error: Executable was not created")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Build failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Build completed successfully!")
