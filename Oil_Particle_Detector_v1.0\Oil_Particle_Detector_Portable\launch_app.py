#!/usr/bin/env python3
"""
Oil Particle Detection System - Launcher with Qt Fix
Fixes Qt platform plugin issues and launches the application
"""

import sys
import os
from pathlib import Path

def fix_qt_plugin_path():
    """Fix Qt plugin path issues"""
    try:
        # Get the virtual environment path
        venv_path = Path(__file__).parent / ".venv"
        
        # Set Qt plugin path
        qt_plugin_path = venv_path / "Lib" / "site-packages" / "PyQt5" / "Qt5" / "plugins"
        if qt_plugin_path.exists():
            os.environ['QT_PLUGIN_PATH'] = str(qt_plugin_path)
            print(f"Set QT_PLUGIN_PATH to: {qt_plugin_path}")
        
        # Set Qt platform plugin
        platform_plugin_path = qt_plugin_path / "platforms"
        if platform_plugin_path.exists():
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(platform_plugin_path)
            print(f"Set QT_QPA_PLATFORM_PLUGIN_PATH to: {platform_plugin_path}")
        
        # Force platform to windows
        os.environ['QT_QPA_PLATFORM'] = 'windows'
        
        return True
    except Exception as e:
        print(f"Warning: Could not set Qt paths: {e}")
        return False

def main():
    """Main launcher function"""
    print("Oil Particle Detection System - Launcher")
    print("=" * 50)
    
    # Fix Qt plugin paths
    print("Fixing Qt plugin paths...")
    fix_qt_plugin_path()
    
    # Add project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        print("Importing PyQt5...")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        print("Creating QApplication...")
        # Create QApplication with proper attributes
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # Set application properties
        app.setApplicationName("Oil Particle Detection System")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Mechanical Engineering Research")
        
        print("Importing main application...")
        # Try to use the modern interface first
        try:
            from oil_detector.gui.modern_interface import ModernParticleInterface
            print("Using modern professional interface...")
            window = ModernParticleInterface()
        except ImportError:
            print("Modern interface not available, using standard interface...")
            from oil_detector import OilParticleDetectorApp
            window = OilParticleDetectorApp()

        print("Creating main window...")
        
        print("Showing window...")
        window.show()
        
        print("Starting application event loop...")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\nTrying alternative import method...")
        
        try:
            # Try importing the old way as fallback
            from oil_particle_detector_app import OilParticleDetectorApp, main as old_main
            print("Using fallback application...")
            return old_main()
        except Exception as e2:
            print(f"❌ Fallback also failed: {e2}")
            print("\nPlease ensure all dependencies are installed:")
            print("pip install PyQt5 opencv-python numpy matplotlib scipy")
            return 1
            
    except Exception as e:
        print(f"❌ Application error: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit_code = main()
    print(f"\nApplication exited with code: {exit_code}")
    sys.exit(exit_code)
