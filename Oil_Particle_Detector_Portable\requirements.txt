# Oil Particle Detection System - Python 3.12 Requirements
# Modern dependencies with RT-DETR for advanced particle detection

# Core GUI Framework
PyQt5>=5.15.0

# Computer Vision and Image Processing
opencv-python>=4.8.0
Pillow>=10.0.0
scikit-image>=0.20.0

# Deep Learning Framework - RT-DETR Requirements
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
ultralytics>=8.0.0

# RT-DETR Specific Dependencies
detectron2>=0.6
fvcore>=0.1.5
iopath>=0.1.9

# Scientific Computing
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scipy>=1.10.0

# Additional ML/AI Libraries
timm>=0.9.0
albumentations>=1.3.0
supervision>=0.16.0

# Utility Libraries
tqdm>=4.65.0
pyyaml>=6.0
requests>=2.31.0
omegaconf>=2.3.0

# Development and Testing (optional)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
