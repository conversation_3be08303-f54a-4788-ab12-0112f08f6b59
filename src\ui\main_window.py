"""
Modern English UI for Oil Particle Detection System - Main Window
Compatible with Python 3.12 and PyQt5
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QFrame, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
import sys
import os

class MainWindow(QMainWindow):
    """Main window for Oil Particle Detection System"""
    
    # Signal to open analysis window
    open_analysis = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Oil Particle Online Monitoring System")
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(600, 400)
        
        # Set window icon if available
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QPixmap(icon_path))
        except:
            pass
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # Create header section
        self.create_header(main_layout)
        
        # Create main button section
        self.create_main_button(main_layout)
        
        # Create footer section
        self.create_footer(main_layout)
        
        # Apply modern styling
        self.apply_styling()
        
    def create_header(self, layout):
        """Create header section with title and description"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.NoFrame)
        header_layout = QVBoxLayout(header_frame)
        
        # Main title
        title_label = QLabel("Oil Particle Online Monitoring System")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin: 20px 0;
                padding: 10px;
            }
        """)
        
        # Subtitle
        subtitle_label = QLabel("Advanced Particle Detection & Wear Analysis")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Arial", 14))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                margin-bottom: 10px;
            }
        """)
        
        # Description
        desc_label = QLabel(
            "Detect and analyze oil particles using RT-DETR (Real-Time Detection Transformer)\n"
            "and CNN-based wear stage classification with advanced morphological analysis"
        )
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setFont(QFont("Arial", 11))
        desc_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                line-height: 1.5;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addWidget(desc_label)
        
        layout.addWidget(header_frame)
        
    def create_main_button(self, layout):
        """Create main action button"""
        button_frame = QFrame()
        button_layout = QVBoxLayout(button_frame)
        button_layout.setAlignment(Qt.AlignCenter)
        
        # Main action button
        self.start_button = QPushButton("Start Oil Particle Analysis")
        self.start_button.setFont(QFont("Arial", 16, QFont.Bold))
        self.start_button.setMinimumSize(300, 80)
        self.start_button.clicked.connect(self.open_analysis_window)
        
        # Button styling
        self.start_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
        """)
        
        button_layout.addWidget(self.start_button)
        layout.addWidget(button_frame)
        
    def create_footer(self, layout):
        """Create footer with system information"""
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        
        # System info
        info_label = QLabel(
            "System Features:\n"
            "• RT-DETR (Real-Time Detection Transformer) for Superior Particle Detection\n"
            "• CNN-based Wear Stage Classification (Early/Middle/Late)\n"
            "• Advanced Morphological & Texture Analysis\n"
            "• Statistical Analysis and Results Export\n"
            "• Modern English Interface with Enhanced Performance"
        )
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 10))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 10px;
                line-height: 1.6;
            }
        """)
        
        # Version info
        version_label = QLabel("Python 3.12 Compatible • Modern PyQt5 Interface")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Arial", 9))
        version_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                margin-top: 10px;
            }
        """)
        
        footer_layout.addWidget(info_label)
        footer_layout.addWidget(version_label)
        
        layout.addWidget(footer_frame)
        
    def apply_styling(self):
        """Apply modern styling to the window"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QWidget {
                background: transparent;
            }
        """)
        
    def open_analysis_window(self):
        """Open the analysis window"""
        self.open_analysis.emit()
        self.hide()  # Hide main window when analysis opens


def main():
    """Main function for testing the window"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Oil Particle Detector")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Oil Analysis Systems")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
