#!/usr/bin/env python3
"""
Oil Particle Detection System - Main Entry Point
Clean, organized entry point for the application
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Main application entry point"""
    try:
        # Import the main application
        from oil_detector import OilParticleDetectorApp
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("Oil Particle Detection System")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Mechanical Engineering Research")
        
        # Create and show main window
        window = OilParticleDetectorApp()
        window.show()
        
        # Run the application
        return app.exec_()
        
    except ImportError as e:
        print(f"Failed to import required modules: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"Application failed to start: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
