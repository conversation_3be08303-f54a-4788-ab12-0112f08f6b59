"""
Edge Artifact Filter for Oil Particle Detection
Removes black edges and shadows that are imaging artifacts, not real particles
"""

import cv2
import numpy as np
from typing import Tuple, List, Optional
import matplotlib.pyplot as plt

class EdgeArtifactFilter:
    """
    Filter to remove edge artifacts and black shadows from microscopy images
    Common in oil particle analysis due to lighting and lens issues
    """
    
    def __init__(self, 
                 edge_threshold: int = 30,
                 min_brightness: int = 40,
                 edge_margin: int = 20,
                 vignetting_detection: bool = True):
        """
        Initialize edge artifact filter
        
        Args:
            edge_threshold: Threshold for detecting dark edges (0-255)
            min_brightness: Minimum brightness to consider as valid particle area
            edge_margin: Margin from image edges to exclude (pixels)
            vignetting_detection: Whether to detect and correct lens vignetting
        """
        self.edge_threshold = edge_threshold
        self.min_brightness = min_brightness
        self.edge_margin = edge_margin
        self.vignetting_detection = vignetting_detection
        
    def detect_edge_artifacts(self, image: np.ndarray) -> np.ndarray:
        """
        Detect edge artifacts and create a mask
        
        Args:
            image: Input image (BGR or RGB)
            
        Returns:
            Binary mask where True = valid area, False = artifact area
        """
        if len(image.shape) == 3:
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        h, w = gray.shape
        mask = np.ones((h, w), dtype=bool)
        
        # 1. Remove edge margins (common artifact area)
        mask[:self.edge_margin, :] = False  # Top edge
        mask[-self.edge_margin:, :] = False  # Bottom edge
        mask[:, :self.edge_margin] = False  # Left edge
        mask[:, -self.edge_margin:] = False  # Right edge
        
        # 2. Detect dark edges (black shadows)
        dark_mask = gray > self.edge_threshold
        mask = mask & dark_mask
        
        # 3. Remove very dark areas (likely artifacts)
        brightness_mask = gray > self.min_brightness
        mask = mask & brightness_mask
        
        # 4. Detect vignetting (circular darkening from lens)
        if self.vignetting_detection:
            vignetting_mask = self._detect_vignetting(gray)
            mask = mask & vignetting_mask
        
        # 5. Morphological operations to clean up mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask_uint8 = mask.astype(np.uint8) * 255
        mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_CLOSE, kernel)
        mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_OPEN, kernel)
        mask = mask_uint8 > 0
        
        return mask
    
    def _detect_vignetting(self, gray: np.ndarray) -> np.ndarray:
        """
        Detect and create mask for vignetting artifacts
        
        Args:
            gray: Grayscale image
            
        Returns:
            Binary mask excluding vignetting areas
        """
        h, w = gray.shape
        center_x, center_y = w // 2, h // 2
        
        # Create coordinate grids
        y, x = np.ogrid[:h, :w]
        
        # Calculate distance from center
        distances = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)
        
        # Normalize distances
        normalized_distances = distances / max_distance
        
        # Create radial brightness profile
        radial_profile = []
        for r in np.linspace(0, 1, 20):
            mask_ring = (normalized_distances >= r - 0.05) & (normalized_distances < r + 0.05)
            if np.any(mask_ring):
                avg_brightness = np.mean(gray[mask_ring])
                radial_profile.append(avg_brightness)
            else:
                radial_profile.append(0)
        
        # Detect significant brightness drop towards edges (vignetting)
        if len(radial_profile) > 5:
            center_brightness = np.mean(radial_profile[:3])
            edge_brightness = np.mean(radial_profile[-3:])
            
            # If edge is significantly darker, apply vignetting correction
            if center_brightness > edge_brightness * 1.5:
                # Create mask excluding dark edges
                vignetting_threshold = edge_brightness * 1.2
                vignetting_mask = gray > vignetting_threshold
                return vignetting_mask
        
        # No significant vignetting detected
        return np.ones_like(gray, dtype=bool)
    
    def filter_detections(self, 
                         detections: List[dict], 
                         image_shape: Tuple[int, int],
                         valid_mask: Optional[np.ndarray] = None) -> List[dict]:
        """
        Filter detections to remove those in artifact areas
        
        Args:
            detections: List of detection dictionaries with bbox coordinates
            image_shape: Shape of the image (height, width)
            valid_mask: Pre-computed valid area mask
            
        Returns:
            Filtered list of detections
        """
        if valid_mask is None:
            # Create a default mask excluding edges
            h, w = image_shape[:2]
            valid_mask = np.ones((h, w), dtype=bool)
            valid_mask[:self.edge_margin, :] = False
            valid_mask[-self.edge_margin:, :] = False
            valid_mask[:, :self.edge_margin] = False
            valid_mask[:, -self.edge_margin:] = False
        
        filtered_detections = []
        
        for detection in detections:
            # Get bounding box coordinates
            if 'bbox' in detection:
                x, y, w, h = detection['bbox']
            elif 'box' in detection:
                x, y, w, h = detection['box']
            else:
                # Skip if no bounding box info
                continue
            
            # Calculate center point of detection
            center_x = int(x + w // 2)
            center_y = int(y + h // 2)
            
            # Check if center is in valid area
            if (0 <= center_y < valid_mask.shape[0] and 
                0 <= center_x < valid_mask.shape[1] and
                valid_mask[center_y, center_x]):
                
                # Additional check: ensure most of the bounding box is in valid area
                x1, y1 = max(0, int(x)), max(0, int(y))
                x2, y2 = min(valid_mask.shape[1], int(x + w)), min(valid_mask.shape[0], int(y + h))
                
                if x2 > x1 and y2 > y1:
                    bbox_mask = valid_mask[y1:y2, x1:x2]
                    valid_ratio = np.sum(bbox_mask) / bbox_mask.size
                    
                    # Keep detection if at least 70% is in valid area
                    if valid_ratio >= 0.7:
                        filtered_detections.append(detection)
        
        return filtered_detections
    
    def preprocess_image(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess image to remove edge artifacts
        
        Args:
            image: Input image
            
        Returns:
            Tuple of (processed_image, valid_mask)
        """
        # Create valid area mask
        valid_mask = self.detect_edge_artifacts(image)
        
        # Create processed image
        processed_image = image.copy()
        
        # Option 1: Set invalid areas to neutral gray
        if len(image.shape) == 3:
            processed_image[~valid_mask] = [128, 128, 128]  # Neutral gray
        else:
            processed_image[~valid_mask] = 128
        
        return processed_image, valid_mask
    
    def visualize_filtering(self, image: np.ndarray, save_path: Optional[str] = None):
        """
        Visualize the edge artifact filtering process
        
        Args:
            image: Input image
            save_path: Optional path to save visualization
        """
        # Get valid mask
        valid_mask = self.detect_edge_artifacts(image)
        processed_image, _ = self.preprocess_image(image)
        
        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Original image
        if len(image.shape) == 3:
            axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            axes[0, 0].imshow(image, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Valid area mask
        axes[0, 1].imshow(valid_mask, cmap='RdYlGn')
        axes[0, 1].set_title('Valid Area Mask\n(Green=Valid, Red=Artifact)')
        axes[0, 1].axis('off')
        
        # Processed image
        if len(processed_image.shape) == 3:
            axes[1, 0].imshow(cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB))
        else:
            axes[1, 0].imshow(processed_image, cmap='gray')
        axes[1, 0].set_title('Processed Image\n(Artifacts Neutralized)')
        axes[1, 0].axis('off')
        
        # Overlay mask on original 