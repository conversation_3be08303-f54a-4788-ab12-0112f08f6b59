# Unified Requirements for Oil Particle Detection System
# Last updated: 2023-11-15

# Core dependencies
PyQt5==5.15.10
opencv-python==********
Pillow==10.1.0
scikit-image==0.22.0

# Deep Learning Framework
torch==2.1.1+cpu
torchvision==0.16.1+cpu
transformers==4.35.2
ultralytics==8.0.206

# Scientific Computing
numpy==1.24.4
pandas==2.1.3
matplotlib==3.8.2
scipy==1.11.4

# ML/AI Libraries
timm==0.9.12
supervision==0.16.0
albumentations>=1.3.0

# Utility Libraries
tqdm==4.66.1
pyyaml==6.0.1
requests==2.31.0
omegaconf==2.3.0

# Application building
PyInstaller==5.13.0

# Development and testing (optional)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Windows-specific (for shortcuts)
pywin32>=227; sys_platform == "win32"
winshell>=0.6; sys_platform == "win32"

# GPU support (commented by default - uncomment to use)
# Replace CPU versions with these for GPU support:
# torch==2.1.1
# torchvision==0.16.1