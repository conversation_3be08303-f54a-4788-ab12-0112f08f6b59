#!/usr/bin/env python3
"""
Project Organization Script
Organizes the messy oil particle detector project into a clean structure
"""

import os
import shutil
import glob
from pathlib import Path

def create_directory_structure():
    """Create the new organized directory structure"""
    
    # Define the new directory structure
    directories = [
        'src/',                    # Main source code
        'src/ui/',                 # UI components
        'src/models/',             # ML models
        'src/utils/',              # Utility functions
        'src/scripts/',            # Scripts and tools
        
        'data/',                   # Data directory
        'data/raw/',               # Raw data
        'data/processed/',         # Processed data
        'data/training/',          # Training data
        'data/test/',              # Test data
        
        'config/',                 # Configuration files
        'weights/',                # Model weights
        'output/',                 # Output files
        'output/results/',         # Analysis results
        'output/visualizations/',  # Charts and plots
        
        'docs/',                   # Documentation
        'docs/guides/',            # User guides
        'docs/reports/',           # Technical reports
        
        'tests/',                  # Test files
        'backup/',                 # Backup of old files
        'temp/'                    # Temporary files
    ]
    
    # Create directories
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def safe_move(src, dst):
    """Safely move a file or directory"""
    try:
        if os.path.exists(src):
            if os.path.isdir(src):
                if os.path.exists(dst):
                    shutil.rmtree(dst)
                shutil.move(src, dst)
                print(f"Moved directory: {src} -> {dst}")
            else:
                if os.path.exists(dst):
                    os.remove(dst)
                shutil.move(src, dst)
                print(f"Moved file: {src} -> {dst}")
        return True
    except Exception as e:
        print(f"Error moving {src}: {e}")
        return False

def move_files():
    """Move files to their appropriate locations"""
    
    # File organization mapping - specific files first
    specific_moves = [
        ('main.py', 'src/'),
        ('main_window.py', 'src/ui/'),
        ('analysis_window.py', 'src/ui/'),
        ('test_rt_detr.py', 'tests/'),
        ('create_test_image.py', 'src/scripts/'),
        ('demo_edge_filtering.py', 'src/scripts/'),
        ('test_edge_filtering.py', 'tests/'),
        ('training_data_overview.py', 'src/scripts/'),
        ('simple_accuracy_report.py', 'src/scripts/'),
        ('create_confusion_matrices.py', 'src/scripts/'),
        ('current_model_evaluation.py', 'src/scripts/'),
        ('evaluate_real_models.py', 'src/scripts/'),
        ('evaluate_accuracy_confusion.py', 'src/scripts/'),
        ('evaluate_models.py', 'src/scripts/'),
        ('mechanical_engineering_93_percent_plan.py', 'src/scripts/'),
        ('quick_93_percent_boost.py', 'src/scripts/'),
        ('implement_93_percent.py', 'src/scripts/'),
        ('diagnose_system.py', 'src/scripts/'),
        ('fix_analysis_button.py', 'src/scripts/'),
        ('requirements.txt', 'config/'),
        ('requirements_essential.txt', 'config/'),
        ('install_dependencies.bat', 'config/'),
        ('run_oil_detector.bat', 'config/'),
        ('README.md', 'docs/'),
        ('ENHANCED_INTERFACE_GUIDE.md', 'docs/guides/'),
        ('PRACTICAL_DETECTION_GUIDE.md', 'docs/guides/'),
        ('RT-DETR_UPGRADE_SUMMARY.md', 'docs/reports/'),
        ('FIXES_APPLIED.md', 'docs/reports/'),
        ('REAL_IMAGES_UPDATE.md', 'docs/reports/'),
        ('FIXES_COMPLETED_SUCCESS_REPORT.md', 'docs/reports/'),
        ('MODEL_ACCURACY_LOSS_SUMMARY.md', 'docs/reports/'),
        ('YOLOV3_TO_TRANSFORMER_MIGRATION_SUMMARY.md', 'docs/reports/'),
        ('ADVANCED_MODEL_DEVELOPMENT_PLAN.md', 'docs/reports/'),
        ('EDGE_FILTERING_IMPLEMENTATION_SUMMARY.md', 'docs/reports/'),
        ('mechanical_engineering_implementation.md', 'docs/reports/'),
        ('accuracy_analysis_report.md', 'docs/reports/'),
        ('model_evaluation_report.json', 'output/results/'),
        ('model_evaluation_summary.csv', 'output/results/'),
        ('current_model_performance.csv', 'output/results/'),
        ('fixed_main.py', 'backup/'),
    ]
    
    # Move specific files
    for src, dst in specific_moves:
        if os.path.exists(src):
            safe_move(src, dst)
    
    # Move directories
    dir_moves = [
        ('models', 'src/models'),
        ('utils', 'src/utils'),
        ('scripts', 'src/scripts'),
        ('config', 'config'),
        ('02_DATA', 'data/raw'),
        ('data', 'data'),
        ('weights', 'weights'),
    ]
    
    for src, dst in dir_moves:
        if os.path.exists(src):
            safe_move(src, dst)
    
    # Move pattern-based files
    pattern_moves = [
        ('*.png', 'output/visualizations/'),
        ('*.jpg', 'output/visualizations/'),
        ('*.jpeg', 'output/visualizations/'),
        ('*.bmp', 'output/visualizations/'),
        ('*.csv', 'output/results/'),
        ('*.json', 'config/'),
    ]
    
    for pattern, dst in pattern_moves:
        matches = glob.glob(pattern)
        for match in matches:
            if os.path.isfile(match):
                filename = os.path.basename(match)
                dst_path = os.path.join(dst, filename)
                safe_move(match, dst_path)
    
    # Move version files to backup
    version_patterns = ['[0-9]*.[0-9]*', '[0-9]*.[0-9]*.[0-9]*']
    for pattern in version_patterns:
        matches = glob.glob(pattern)
        for match in matches:
            if os.path.isfile(match):
                safe_move(match, 'backup/')

def cleanup_temp_files():
    """Clean up temporary and cache files"""
    
    # Remove cache directories
    cache_dirs = ['__pycache__', '.pytest_cache', '.mypy_cache']
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"Removed cache directory: {cache_dir}")
            except Exception as e:
                print(f"Error removing {cache_dir}: {e}")
    
    # Remove temporary files
    temp_patterns = ['*.tmp', '*.temp', '*.log']
    for pattern in temp_patterns:
        for file in glob.glob(pattern):
            try:
                os.remove(file)
                print(f"Removed temp file: {file}")
            except Exception as e:
                print(f"Error removing {file}: {e}")

def create_new_main():
    """Create a new main.py in the root that points to src/main.py"""
    
    main_content = '''#!/usr/bin/env python3
"""
Oil Particle Detection System - Main Entry Point
Organized project structure
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import and run main application
from main import main

if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open('main.py', 'w') as f:
        f.write(main_content)
    print("Created new main.py entry point")

def create_project_structure_file():
    """Create a file documenting the new project structure"""
    
    structure_content = '''# Project Structure

## Overview
This project has been reorganized for better maintainability and clarity.

## Directory Structure

```
oil_particle_detector_py312/
├── main.py                     # Main entry point
├── src/                        # Source code
│   ├── main.py                 # Main application logic
│   ├── ui/                     # User interface components
│   │   ├── main_window.py
│   │   └── analysis_window.py
│   ├── models/                 # Machine learning models
│   │   ├── rt_detr_detector.py
│   │   ├── practical_detector.py
│   │   └── particle_analyzer.py
│   ├── utils/                  # Utility functions
│   └── scripts/                # Tools and scripts
├── data/                       # Data files
│   ├── raw/                    # Raw data
│   ├── processed/              # Processed data
│   ├── training/               # Training data
│   └── test/                   # Test data
├── config/                     # Configuration files
│   ├── requirements.txt
│   ├── *.json
│   └── *.bat
├── weights/                    # Model weights
├── output/                     # Output files
│   ├── results/                # Analysis results
│   └── visualizations/         # Charts and plots
├── docs/                       # Documentation
│   ├── guides/                 # User guides
│   └── reports/                # Technical reports
├── tests/                      # Test files
├── backup/                     # Backup of old files
└── temp/                       # Temporary files
```

## Key Changes
- Moved all source code to `src/` directory
- Organized UI components in `src/ui/`
- Grouped models in `src/models/`
- Centralized configuration in `config/`
- Separated documentation in `docs/`
- Created dedicated output directories
- Moved old version files to `backup/`

## Usage
- Run the application: `python main.py`
- Run tests: `python -m pytest tests/`
- Install dependencies: `pip install -r config/requirements.txt`
'''
    
    with open('PROJECT_STRUCTURE.md', 'w') as f:
        f.write(structure_content)
    print("Created PROJECT_STRUCTURE.md")

def main():
    """Main organization function"""
    print("Starting project organization...")
    print("=" * 50)
    
    # Create new directory structure
    create_directory_structure()
    print()
    
    # Move files to appropriate locations
    move_files()
    print()
    
    # Clean up temporary files
    cleanup_temp_files()
    print()
    
    # Create new main entry point
    create_new_main()
    print()
    
    # Create project structure documentation
    create_project_structure_file()
    print()
    
    print("=" * 50)
    print("Project organization completed!")
    print("Check PROJECT_STRUCTURE.md for the new layout.")
    print("Run 'python main.py' to start the application.")

if __name__ == "__main__":
    main() 