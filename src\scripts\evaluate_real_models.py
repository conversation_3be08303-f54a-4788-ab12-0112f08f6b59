"""
Real Model Evaluation Script
Evaluates actual trained models with confusion matrix and accuracy
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
import pandas as pd
import json
import torch
import cv2
from pathlib import Path
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Add training directory to path
sys.path.append('training')

class RealModelEvaluator:
    """
    Evaluator for actual trained models in the oil particle detection system
    """
    
    def __init__(self):
        self.class_names = ['Lamellar', 'Spherical', 'Normal', 'Sliding', 'Cutting']
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 Using device: {self.device}")
        
    def load_transformer_model(self):
        """Load the transformer-based detection model"""
        try:
            from transformers import AutoModelForObjectDetection, AutoImageProcessor
            
            # Try to load fine-tuned model first
            model_path = "training/models/transformer_detector"
            if os.path.exists(model_path):
                print("📦 Loading fine-tuned transformer model...")
                model = AutoModelForObjectDetection.from_pretrained(model_path)
                processor = AutoImageProcessor.from_pretrained(model_path)
            else:
                print("📦 Loading pre-trained DETR model...")
                model_name = "facebook/detr-resnet-50"
                model = AutoModelForObjectDetection.from_pretrained(model_name)
                processor = AutoImageProcessor.from_pretrained(model_name)
            
            model.to(self.device)
            model.eval()
            
            return model, processor
            
        except Exception as e:
            print(f"❌ Failed to load transformer model: {e}")
            return None, None
    
    def load_resnet_model(self):
        """Load the ResNet50d classification model"""
        try:
            import timm
            
            # Load ResNet50d model
            model_path = "models/resnet50d_model.pth"
            if os.path.exists(model_path):
                print("📦 Loading ResNet50d model...")
                model = timm.create_model('resnet50d', num_classes=len(self.class_names))
                
                # Load state dict with proper key handling
                state_dict = torch.load(model_path, map_location=self.device)
                
                # Handle potential key mismatch
                if 'model.fc.weight' in state_dict:
                    # Remove 'model.' prefix if present
                    new_state_dict = {}
                    for key, value in state_dict.items():
                        new_key = key.replace('model.', '') if key.startswith('model.') else key
                        new_state_dict[new_key] = value
                    state_dict = new_state_dict
                
                model.load_state_dict(state_dict, strict=False)
                model.to(self.device)
                model.eval()
                
                return model
            else:
                print("⚠️ ResNet50d model file not found, creating new model...")
                model = timm.create_model('resnet50d', num_classes=len(self.class_names), pretrained=True)
                model.to(self.device)
                model.eval()
                return model
                
        except Exception as e:
            print(f"❌ Failed to load ResNet model: {e}")
            return None
    
    def load_validation_data(self):
        """Load validation images and annotations"""
        print("📊 Loading validation data...")
        
        # Load COCO annotations
        val_annotations_path = Path("data/particle_coco/annotations_val.json")
        val_images_path = Path("data/particle_coco/val")
        
        if not val_annotations_path.exists():
            print("❌ Validation annotations not found")
            return [], []
        
        with open(val_annotations_path, 'r') as f:
            coco_data = json.load(f)
        
        # Prepare data
        images = []
        annotations = []
        
        # Create image ID to info mapping
        image_info = {img['id']: img for img in coco_data['images']}
        
        # Group annotations by image
        image_annotations = defaultdict(list)
        for ann in coco_data['annotations']:
            image_annotations[ann['image_id']].append(ann)
        
        # Load images with annotations
        for img_id, img_info in image_info.items():
            img_path = val_images_path / img_info['file_name']
            
            if img_path.exists():
                # Load image
                image = cv2.imread(str(img_path))
                if image is not None:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    images.append(image)
                    
                    # Get annotations for this image
                    img_annotations = image_annotations.get(img_id, [])
                    # Use the most prominent class in the image
                    if img_annotations:
                        # Take the largest bounding box as the main class
                        main_ann = max(img_annotations, key=lambda x: x['bbox'][2] * x['bbox'][3])
                        annotations.append(main_ann['category_id'] - 1)  # Convert to 0-based
                    else:
                        annotations.append(0)  # Default class
        
        print(f"✅ Loaded {len(images)} validation images")
        return images, annotations
    
    def predict_with_transformer(self, model, processor, images):
        """Make predictions using transformer model"""
        predictions = []
        
        print("🔍 Making transformer predictions...")
        for i, image in enumerate(images):
            try:
                # Preprocess image
                inputs = processor(images=image, return_tensors="pt")
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                # Make prediction
                with torch.no_grad():
                    outputs = model(**inputs)
                
                # Process outputs (simplified - take highest confidence detection)
                logits = outputs.logits[0]
                probs = torch.softmax(logits, dim=-1)
                
                # Get prediction (simplified approach)
                if len(probs) > 0:
                    pred_class = torch.argmax(probs[0]).item()
                    # Map to our classes (simplified)
                    pred_class = pred_class % len(self.class_names)
                else:
                    pred_class = 0  # Default prediction
                
                predictions.append(pred_class)
                
                if (i + 1) % 10 == 0:
                    print(f"   Processed {i + 1}/{len(images)} images")
                    
            except Exception as e:
                print(f"⚠️ Error processing image {i}: {e}")
                predictions.append(0)  # Default prediction
        
        return predictions
    
    def predict_with_resnet(self, model, images):
        """Make predictions using ResNet model"""
        predictions = []
        
        print("🔍 Making ResNet predictions...")
        for i, image in enumerate(images):
            try:
                # Preprocess image
                image_resized = cv2.resize(image, (224, 224))
                image_tensor = torch.from_numpy(image_resized).float()
                image_tensor = image_tensor.permute(2, 0, 1) / 255.0
                image_tensor = image_tensor.unsqueeze(0).to(self.device)
                
                # Normalize (ImageNet stats)
                mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1).to(self.device)
                std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1).to(self.device)
                image_tensor = (image_tensor - mean) / std
                
                # Make prediction
                with torch.no_grad():
                    outputs = model(image_tensor)
                    pred_class = torch.argmax(outputs, dim=1).item()
                
                predictions.append(pred_class)
                
                if (i + 1) % 10 == 0:
                    print(f"   Processed {i + 1}/{len(images)} images")
                    
            except Exception as e:
                print(f"⚠️ Error processing image {i}: {e}")
                predictions.append(0)  # Default prediction
        
        return predictions
    
    def plot_confusion_matrix(self, y_true, y_pred, model_name):
        """Plot confusion matrix"""
        cm = confusion_matrix(y_true, y_pred, labels=range(len(self.class_names)))
        
        plt.figure(figsize=(10, 8))
        
        # Calculate percentages
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
        
        # Plot heatmap
        sns.heatmap(cm_percent, 
                   annot=True, 
                   fmt='.1f',
                   cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names,
                   cbar_kws={'label': 'Percentage (%)'})
        
        accuracy = accuracy_score(y_true, y_pred)
        plt.title(f'Confusion Matrix - {model_name}\nAccuracy: {accuracy:.1%}', 
                 fontsize=14, fontweight='bold')
        plt.xlabel('Predicted Class', fontsize=12)
        plt.ylabel('True Class', fontsize=12)
        
        # Add count annotations
        for i in range(len(self.class_names)):
            for j in range(len(self.class_names)):
                plt.text(j + 0.5, i + 0.7, f'({cm[i, j]})', 
                        ha='center', va='center', fontsize=9, color='gray')
        
        plt.tight_layout()
        
        # Save plot
        save_path = f"real_confusion_matrix_{model_name.lower().replace(' ', '_')}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ Confusion matrix saved: {save_path}")
        
        plt.show()
        
        return cm, accuracy
    
    def evaluate_models(self):
        """Evaluate all available models"""
        print("🚀 EVALUATING REAL TRAINED MODELS")
        print("="*50)
        
        # Load validation data
        images, y_true = self.load_validation_data()
        
        if len(images) == 0:
            print("❌ No validation data available")
            return
        
        results = {}
        
        # Evaluate Transformer Model
        transformer_model, processor = self.load_transformer_model()
        if transformer_model is not None:
            print("\n🤖 Evaluating Transformer Detector...")
            y_pred_transformer = self.predict_with_transformer(transformer_model, processor, images)
            cm_transformer, acc_transformer = self.plot_confusion_matrix(
                y_true, y_pred_transformer, "Transformer Detector"
            )
            results['Transformer Detector'] = {
                'accuracy': acc_transformer,
                'predictions': y_pred_transformer
            }
            print(f"✅ Transformer Detector Accuracy: {acc_transformer:.1%}")
        
        # Evaluate ResNet Model
        resnet_model = self.load_resnet_model()
        if resnet_model is not None:
            print("\n🧠 Evaluating ResNet50d Classifier...")
            y_pred_resnet = self.predict_with_resnet(resnet_model, images)
            cm_resnet, acc_resnet = self.plot_confusion_matrix(
                y_true, y_pred_resnet, "ResNet50d Classifier"
            )
            results['ResNet50d Classifier'] = {
                'accuracy': acc_resnet,
                'predictions': y_pred_resnet
            }
            print(f"✅ ResNet50d Classifier Accuracy: {acc_resnet:.1%}")
        
        # Create ensemble if both models available
        if len(results) >= 2:
            print("\n🎯 Creating Ensemble Predictions...")
            model_names = list(results.keys())
            pred1 = results[model_names[0]]['predictions']
            pred2 = results[model_names[1]]['predictions']
            
            # Simple ensemble: majority vote with transformer preference
            y_pred_ensemble = []
            for p1, p2 in zip(pred1, pred2):
                if p1 == p2:
                    y_pred_ensemble.append(p1)
                else:
                    # Transformer has higher weight
                    y_pred_ensemble.append(p1)  # Prefer transformer
            
            cm_ensemble, acc_ensemble = self.plot_confusion_matrix(
                y_true, y_pred_ensemble, "Ensemble Model"
            )
            results['Ensemble Model'] = {
                'accuracy': acc_ensemble,
                'predictions': y_pred_ensemble
            }
            print(f"✅ Ensemble Model Accuracy: {acc_ensemble:.1%}")
        
        # Summary
        print("\n" + "="*50)
        print("📊 FINAL RESULTS SUMMARY")
        print("="*50)
        
        for model_name, result in results.items():
            print(f"{model_name}: {result['accuracy']:.1%}")
        
        if results:
            best_model = max(results.items(), key=lambda x: x[1]['accuracy'])
            print(f"\n🏆 Best Model: {best_model[0]} ({best_model[1]['accuracy']:.1%})")
        
        return results


def main():
    """Main evaluation function"""
    evaluator = RealModelEvaluator()
    results = evaluator.evaluate_models()
    
    if results:
        print("\n🎉 Real model evaluation completed!")
        print("Check the generated confusion matrix images.")
    else:
        print("\n⚠️ No models could be evaluated. Please ensure models are trained and available.")


if __name__ == '__main__':
    main()
