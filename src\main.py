"""
Modern Oil Particle Detection System - Main Application
Compatible with Python 3.12 and PyQt5

This is the main entry point for the oil particle detection system.
It provides a modern English interface for particle detection and wear analysis.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# Import our custom windows
from main_window import MainWindow
from analysis_window import AnalysisWindow

class OilParticleDetectorApp:
    """Main application class for Oil Particle Detector"""

    def __init__(self):
        # Enable high DPI scaling before creating QApplication
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        self.app = QApplication(sys.argv)
        self.setup_application()
        
        # Initialize windows
        self.main_window = None
        self.analysis_window = None
        
        self.create_main_window()
        
    def setup_application(self):
        """Setup application properties and styling"""
        # Set application properties
        self.app.setApplicationName("Oil Particle Detector")
        self.app.setApplicationVersion("2.0 - Python 3.12")
        self.app.setOrganizationName("Oil Analysis Systems")
        self.app.setOrganizationDomain("oilanalysis.com")
        
        # Set application icon if available
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
        except:
            pass
            
        # High DPI scaling already set before QApplication creation
        
        # Set global application style
        self.app.setStyleSheet("""
            QApplication {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 10pt;
            }
            QToolTip {
                background-color: #2c3e50;
                color: white;
                border: 1px solid #34495e;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        
    def create_main_window(self):
        """Create and setup the main window"""
        self.main_window = MainWindow()
        
        # Connect signals
        self.main_window.open_analysis.connect(self.show_analysis_window)
        
        # Show main window
        self.main_window.show()
        
    def show_analysis_window(self):
        """Show the analysis window"""
        if self.analysis_window is None:
            self.analysis_window = AnalysisWindow()
            self.analysis_window.back_to_main.connect(self.show_main_window)
            
        self.analysis_window.show()
        
    def show_main_window(self):
        """Show the main window"""
        if self.main_window:
            self.main_window.show()
            
    def run(self):
        """Run the application"""
        try:
            return self.app.exec_()
        except Exception as e:
            # Show error dialog
            error_dialog = QMessageBox()
            error_dialog.setIcon(QMessageBox.Critical)
            error_dialog.setWindowTitle("Application Error")
            error_dialog.setText("An unexpected error occurred:")
            error_dialog.setDetailedText(str(e))
            error_dialog.exec_()
            return 1


def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        ('cv2', 'opencv-python'),
        ('torch', 'torch'),
        ('PIL', 'Pillow'),
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib'),
        ('pandas', 'pandas'),
        ('skimage', 'scikit-image'),
        ('transformers', 'transformers'),
        ('timm', 'timm')
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
        
    return True


def main():
    """Main function"""
    print("=" * 60)
    print("Oil Particle Detection System - Python 3.12 Compatible")
    print("Modern English Interface with Advanced Detection")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return 1
        
    print(f"Python version: {sys.version}")
    
    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        return 1
        
    print("All dependencies found!")
    
    # Check if model files exist (YOLOv3 removed, using transformer-based detection)
    model_files = [
        "weights/resnet50d_5epochs_accuracy0.80357_weights.pth"
    ]

    missing_files = []
    for file_path in model_files:
        full_path = os.path.join(os.path.dirname(__file__), file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)

    if missing_files:
        print("\nWarning: Some model files are missing:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\nThe application will run with transformer-based detection and simulation modes.")
        print("ResNet50d model is required for wear classification accuracy.")
    else:
        print("\n✅ All required model files found!")
        print("🤖 Using modern transformer-based detection (replaces YOLOv3)")
        print("🧠 ResNet50d available for wear classification")
    
    print("\nStarting Oil Particle Detection System...")
    
    # Create and run application
    try:
        app = OilParticleDetectorApp()
        return app.run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
