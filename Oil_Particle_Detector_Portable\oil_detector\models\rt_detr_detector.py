"""
RT-DETR (Real-Time Detection Transformer) for Oil Particle Detection
Modern transformer-based object detection replacing YOLOv3
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from transformers import RTDetrForObjectDetection, RTDetrImageProcessor
import cv2
import numpy as np
from PIL import Image
import logging
from typing import List, Dict, Tuple, Optional
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RTDETRParticleDetector:
    """RT-DETR based oil particle detector"""
    
    def __init__(self, model_name: str = "microsoft/rt-detr-resnet50d", device: str = "auto"):
        """
        Initialize RT-DETR detector
        
        Args:
            model_name: Hugging Face model name for RT-DETR
            device: Device to run inference on ('cpu', 'cuda', or 'auto')
        """
        self.model_name = model_name
        self.device = self._get_device(device)
        self.model = None
        self.processor = None
        self.class_names = self._get_particle_classes()
        self.confidence_threshold = 0.5
        self.load_model()
        
    def _get_device(self, device: str) -> torch.device:
        """Get the appropriate device for inference"""
        if device == "auto":
            return torch.device("cuda" if torch.cuda.is_available() else "cpu")
        return torch.device(device)
        
    def _get_particle_classes(self) -> List[str]:
        """Define oil particle classes"""
        return [
            "normal_particle",
            "cutting_particle", 
            "sliding_particle",
            "spherical_particle",
            "lamellar_particle",
            "fatigue_particle",
            "wear_particle"
        ]
        
    def load_model(self):
        """Load RT-DETR model and processor"""
        try:
            logger.info(f"Loading RT-DETR model: {self.model_name}")
            
            # Load processor for image preprocessing
            self.processor = RTDetrImageProcessor.from_pretrained(self.model_name)

            # Load model
            self.model = RTDetrForObjectDetection.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"RT-DETR model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load RT-DETR model: {e}")
            # Fallback to simulation mode
            self.model = None
            self.processor = None
            logger.warning("Running in simulation mode")
            
    def preprocess_image(self, image: np.ndarray) -> Dict:
        """
        Preprocess image for RT-DETR inference
        
        Args:
            image: Input image as numpy array (BGR format from OpenCV)
            
        Returns:
            Preprocessed inputs for the model
        """
        # Convert BGR to RGB
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
            
        # Convert to PIL Image
        pil_image = Image.fromarray(image_rgb)
        
        # Use processor to prepare inputs
        if self.processor:
            inputs = self.processor(images=pil_image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            return inputs
        else:
            # Fallback preprocessing
            transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize((640, 640)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            tensor_image = transform(image_rgb).unsqueeze(0).to(self.device)
            return {"pixel_values": tensor_image}
            
    def detect_particles(self, image: np.ndarray) -> List[Dict]:
        """
        Detect oil particles in image using RT-DETR
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of detected particles with bounding boxes and classifications
        """
        if self.model is None:
            # Simulation mode
            return self._simulate_detection(image)
            
        try:
            # Preprocess image
            inputs = self.preprocess_image(image)
            
            # Run inference
            with torch.no_grad():
                outputs = self.model(**inputs)
                
            # Post-process results
            detections = self._postprocess_outputs(outputs, image.shape)
            
            logger.info(f"Detected {len(detections)} particles")
            return detections
            
        except Exception as e:
            logger.error(f"Detection failed: {e}")
            return self._simulate_detection(image)
            
    def _postprocess_outputs(self, outputs, image_shape: Tuple[int, int, int]) -> List[Dict]:
        """
        Post-process RT-DETR outputs to extract detections
        
        Args:
            outputs: Raw model outputs
            image_shape: Original image shape (H, W, C)
            
        Returns:
            List of detection dictionaries
        """
        detections = []
        
        # Extract predictions
        logits = outputs.logits[0]  # (num_queries, num_classes)
        boxes = outputs.pred_boxes[0]  # (num_queries, 4)
        
        # Apply confidence threshold
        probs = torch.nn.functional.softmax(logits, -1)
        scores, labels = probs[..., :-1].max(-1)
        
        # Filter by confidence
        keep = scores > self.confidence_threshold
        scores = scores[keep]
        labels = labels[keep]
        boxes = boxes[keep]
        
        # Convert to image coordinates
        h, w = image_shape[:2]
        
        for score, label, box in zip(scores, labels, boxes):
            # Convert normalized coordinates to pixel coordinates
            x_center, y_center, width, height = box.cpu().numpy()
            
            x1 = int((x_center - width/2) * w)
            y1 = int((y_center - height/2) * h)
            x2 = int((x_center + width/2) * w)
            y2 = int((y_center + height/2) * h)
            
            # Ensure coordinates are within image bounds
            x1 = max(0, min(x1, w-1))
            y1 = max(0, min(y1, h-1))
            x2 = max(0, min(x2, w-1))
            y2 = max(0, min(y2, h-1))
            
            detection = {
                'bbox': [x1, y1, x2, y2],
                'confidence': float(score.cpu().numpy()),
                'class_id': int(label.cpu().numpy()),
                'class_name': self.class_names[int(label.cpu().numpy())] if int(label.cpu().numpy()) < len(self.class_names) else 'unknown',
                'area': (x2 - x1) * (y2 - y1),
                'center': [(x1 + x2) // 2, (y1 + y2) // 2]
            }
            
            detections.append(detection)
            
        return detections
        
    def _simulate_detection(self, image: np.ndarray) -> List[Dict]:
        """
        Simulate particle detection for testing/fallback
        
        Args:
            image: Input image
            
        Returns:
            Simulated detection results
        """
        import random
        
        h, w = image.shape[:2]
        num_particles = random.randint(10, 40)
        detections = []
        
        for i in range(num_particles):
            # Random bounding box
            x1 = random.randint(0, w - 50)
            y1 = random.randint(0, h - 50)
            x2 = x1 + random.randint(10, 50)
            y2 = y1 + random.randint(10, 50)
            
            detection = {
                'bbox': [x1, y1, x2, y2],
                'confidence': random.uniform(0.6, 0.95),
                'class_id': random.randint(0, len(self.class_names) - 1),
                'class_name': random.choice(self.class_names),
                'area': (x2 - x1) * (y2 - y1),
                'center': [(x1 + x2) // 2, (y1 + y2) // 2]
            }
            
            detections.append(detection)
            
        logger.info(f"Simulated {len(detections)} particle detections")
        return detections
        
    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Visualize detections on image
        
        Args:
            image: Original image
            detections: List of detections
            
        Returns:
            Image with visualized detections
        """
        vis_image = image.copy()
        
        # Define colors for different particle types
        colors = {
            'normal_particle': (0, 255, 0),      # Green
            'cutting_particle': (0, 0, 255),     # Red
            'sliding_particle': (0, 165, 255),   # Orange
            'spherical_particle': (255, 0, 255), # Magenta
            'lamellar_particle': (128, 0, 128),  # Purple
            'fatigue_particle': (255, 255, 0),   # Cyan
            'wear_particle': (128, 128, 128)     # Gray
        }
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            class_name = detection['class_name']
            confidence = detection['confidence']
            
            # Get color for particle type
            color = colors.get(class_name, (255, 255, 255))
            
            # Draw bounding box
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            
            # Background for text
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # Text
            cv2.putText(vis_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                       
        return vis_image
        
    def set_confidence_threshold(self, threshold: float):
        """Set confidence threshold for detections"""
        self.confidence_threshold = max(0.0, min(1.0, threshold))
        logger.info(f"Confidence threshold set to {self.confidence_threshold}")


def test_rt_detr_detector():
    """Test function for RT-DETR detector"""
    detector = RTDETRParticleDetector()
    
    # Create test image
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Run detection
    detections = detector.detect_particles(test_image)
    
    print(f"Detected {len(detections)} particles")
    for i, det in enumerate(detections[:5]):  # Show first 5
        print(f"Particle {i+1}: {det['class_name']} (confidence: {det['confidence']:.3f})")
        
    # Visualize
    vis_image = detector.visualize_detections(test_image, detections)
    
    return detections, vis_image


if __name__ == "__main__":
    test_rt_detr_detector()
