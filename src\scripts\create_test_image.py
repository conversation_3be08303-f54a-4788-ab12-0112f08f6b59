"""
Create a test image with simulated oil particles for testing the detection system
"""

import cv2
import numpy as np
import random

def create_test_particle_image():
    """Create a test image with various particle shapes and sizes"""
    
    # Create a blank image (simulating microscope background)
    width, height = 800, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Add some noise to simulate real microscope conditions
    noise = np.random.normal(0, 10, (height, width, 3))
    image = np.clip(image + noise, 0, 255).astype(np.uint8)
    
    # Convert to grayscale for particle drawing
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Create various types of particles
    particles_created = 0
    
    # 1. Circular particles (normal wear particles)
    for _ in range(15):
        center = (random.randint(50, width-50), random.randint(50, height-50))
        radius = random.randint(5, 25)
        intensity = random.randint(50, 150)
        cv2.circle(gray, center, radius, intensity, -1)
        particles_created += 1
    
    # 2. Elongated particles (cutting particles)
    for _ in range(8):
        center = (random.randint(50, width-50), random.randint(50, height-50))
        axes = (random.randint(30, 60), random.randint(8, 15))
        angle = random.randint(0, 180)
        intensity = random.randint(40, 120)
        cv2.ellipse(gray, center, axes, angle, 0, 360, intensity, -1)
        particles_created += 1
    
    # 3. Irregular particles (severe wear)
    for _ in range(6):
        # Create irregular shapes using random polygons
        center = (random.randint(100, width-100), random.randint(100, height-100))
        num_points = random.randint(6, 12)
        points = []
        
        for _ in range(num_points):
            angle = random.uniform(0, 2 * np.pi)
            radius = random.randint(10, 30)
            x = int(center[0] + radius * np.cos(angle))
            y = int(center[1] + radius * np.sin(angle))
            points.append([x, y])
        
        points = np.array(points, dtype=np.int32)
        intensity = random.randint(60, 140)
        cv2.fillPoly(gray, [points], intensity)
        particles_created += 1
    
    # 4. Small spherical particles
    for _ in range(20):
        center = (random.randint(30, width-30), random.randint(30, height-30))
        radius = random.randint(2, 8)
        intensity = random.randint(70, 160)
        cv2.circle(gray, center, radius, intensity, -1)
        particles_created += 1
    
    # 5. Rectangular particles (lamellar wear)
    for _ in range(5):
        x1 = random.randint(50, width-100)
        y1 = random.randint(50, height-50)
        w = random.randint(20, 50)
        h = random.randint(5, 15)
        intensity = random.randint(50, 130)
        cv2.rectangle(gray, (x1, y1), (x1 + w, y1 + h), intensity, -1)
        particles_created += 1
    
    # Convert back to BGR for saving
    result_image = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    
    # Add some color variation to make it more realistic
    result_image[:,:,0] = np.clip(result_image[:,:,0] * 0.9, 0, 255)  # Reduce blue
    result_image[:,:,2] = np.clip(result_image[:,:,2] * 1.1, 0, 255)  # Increase red slightly
    
    print(f"Created test image with approximately {particles_created} particles")
    return result_image

def create_multiple_test_images():
    """Create multiple test images with different particle densities"""
    
    # Test image 1: Low particle density (early wear)
    image1 = create_test_particle_image()
    cv2.imwrite('test_images/low_density_particles.jpg', image1)
    
    # Test image 2: Medium particle density (middle wear)
    image2 = create_test_particle_image()
    # Add more particles
    gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
    for _ in range(15):
        center = (random.randint(30, 770), random.randint(30, 570))
        radius = random.randint(3, 12)
        intensity = random.randint(60, 140)
        cv2.circle(gray2, center, radius, intensity, -1)
    image2 = cv2.cvtColor(gray2, cv2.COLOR_GRAY2BGR)
    cv2.imwrite('test_images/medium_density_particles.jpg', image2)
    
    # Test image 3: High particle density (late wear)
    image3 = create_test_particle_image()
    gray3 = cv2.cvtColor(image3, cv2.COLOR_BGR2GRAY)
    for _ in range(30):
        center = (random.randint(20, 780), random.randint(20, 580))
        radius = random.randint(2, 15)
        intensity = random.randint(50, 150)
        cv2.circle(gray3, center, radius, intensity, -1)
    image3 = cv2.cvtColor(gray3, cv2.COLOR_GRAY2BGR)
    cv2.imwrite('test_images/high_density_particles.jpg', image3)
    
    print("Created 3 test images with different particle densities")

if __name__ == "__main__":
    import os
    
    # Create test_images directory if it doesn't exist
    if not os.path.exists('test_images'):
        os.makedirs('test_images')
    
    # Create test images
    create_multiple_test_images()
    
    print("Test images created successfully!")
    print("Files created:")
    print("- test_images/low_density_particles.jpg")
    print("- test_images/medium_density_particles.jpg") 
    print("- test_images/high_density_particles.jpg")
