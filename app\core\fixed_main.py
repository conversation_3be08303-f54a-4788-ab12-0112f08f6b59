"""
Fixed Main Application for Oil Particle Detection
Uses the reliable SimpleAnalysisWindow to ensure the system works
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, QGridLayout,
                             QGroupBox, QSplashScreen, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QImage

# Import the fixed analysis window
from fix_analysis_button import SimpleAnalysisWindow

class MainWindow(QMainWindow):
    """Main window for oil particle detection system"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize windows
        self.analysis_window = None
        
        # Initialize UI
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Oil Particle Detection System v2.0")
        self.setGeometry(100, 100, 1000, 700)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Main content
        content = self.create_content()
        main_layout.addWidget(content)
        
        # Footer
        footer = self.create_footer()
        main_layout.addWidget(footer)
    
    def create_header(self):
        """Create the header section"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        header.setMaximumHeight(100)
        
        layout = QHBoxLayout(header)
        
        # Logo/Title
        title = QLabel("Oil Particle Detection System")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        layout.addWidget(title)
        
        # Version info
        version = QLabel("Version 2.0 - Enhanced Edition")
        version.setFont(QFont("Arial", 10))
        version.setAlignment(Qt.AlignRight | Qt.AlignBottom)
        layout.addWidget(version)
        
        return header
    
    def create_content(self):
        """Create the main content section"""
        content = QFrame()
        content.setFrameStyle(QFrame.StyledPanel)
        
        layout = QGridLayout(content)
        
        # Feature boxes
        detection_box = self.create_feature_box(
            "Oil Particle Analysis",
            "Detect and analyze oil particles with advanced AI",
            "Start Oil Particle Analysis",
            self.open_analysis_window
        )
        layout.addWidget(detection_box, 0, 0)
        
        report_box = self.create_feature_box(
            "Wear Analysis Reports",
            "Generate comprehensive wear analysis reports",
            "Generate Reports",
            self.show_report_message
        )
        layout.addWidget(report_box, 0, 1)
        
        database_box = self.create_feature_box(
            "Particle Database",
            "Access the particle reference database",
            "Open Database",
            self.show_database_message
        )
        layout.addWidget(database_box, 1, 0)
        
        settings_box = self.create_feature_box(
            "System Settings",
            "Configure detection parameters and preferences",
            "Open Settings",
            self.show_settings_message
        )
        layout.addWidget(settings_box, 1, 1)
        
        return content
    
    def create_feature_box(self, title, description, button_text, button_action):
        """Create a feature box with title, description and button"""
        box = QGroupBox(title)
        box.setFont(QFont("Arial", 14, QFont.Bold))
        
        layout = QVBoxLayout(box)
        
        # Description
        desc = QLabel(description)
        desc.setWordWrap(True)
        desc.setFont(QFont("Arial", 11))
        layout.addWidget(desc)
        
        # Spacer
        layout.addStretch()
        
        # Button
        button = QPushButton(button_text)
        button.setFont(QFont("Arial", 12))
        button.setMinimumHeight(40)
        button.clicked.connect(button_action)
        layout.addWidget(button)
        
        return box
    
    def create_footer(self):
        """Create the footer section"""
        footer = QFrame()
        footer.setFrameStyle(QFrame.StyledPanel)
        footer.setMaximumHeight(50)
        
        layout = QHBoxLayout(footer)
        
        # Status
        status = QLabel("System Ready - Edge Artifact Filtering Enabled")
        status.setFont(QFont("Arial", 10))
        layout.addWidget(status)
        
        # Copyright
        copyright = QLabel("© 2024 Oil Particle Detection System")
        copyright.setFont(QFont("Arial", 10))
        copyright.setAlignment(Qt.AlignRight)
        layout.addWidget(copyright)
        
        return footer
    
    def open_analysis_window(self):
        """Open the analysis window"""
        try:
            if self.analysis_window is None:
                self.analysis_window = SimpleAnalysisWindow()
                self.analysis_window.back_to_main.connect(self.show_main)
            
            self.hide()
            self.analysis_window.show()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open analysis window:\n{e}")
    
    def show_main(self):
        """Show the main window"""
        self.show()
    
    def show_report_message(self):
        """Show report generation message"""
        QMessageBox.information(self, "Reports", "Report generation will be available in the next update.")
    
    def show_database_message(self):
        """Show database message"""
        QMessageBox.information(self, "Database", "Particle database will be available in the next update.")
    
    def show_settings_message(self):
        """Show settings message"""
        QMessageBox.information(self, "Settings", "Settings will be available in the next update.")

def show_splash_screen():
    """Show splash screen during startup"""
    splash_pix = QPixmap(500, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
    
    # Create splash content
    splash_label = QLabel(splash)
    splash_label.setGeometry(0, 0, 500, 300)
    
    splash_text = """
    <div style="text-align: center; margin-top: 50px;">
        <h1 style="font-size: 24px; color: #2c3e50;">Oil Particle Detection System</h1>
        <p style="font-size: 16px; color: #7f8c8d;">Version 2.0 - Enhanced Edition</p>
        <p style="margin-top: 30px; font-size: 14px;">Loading system components...</p>
        <p style="margin-top: 50px; font-size: 12px;">Edge Artifact Filtering Enabled</p>
    </div>
    """
    
    splash_label.setText(splash_text)
    splash_label.setStyleSheet("background-color: white;")
    
    splash.show()
    
    return splash

def main():
    """Main application function"""
    app = QApplication(sys.argv)
    
    # Show splash screen
    splash = show_splash_screen()
    
    # Create main window
    window = MainWindow()
    
    # Close splash after delay
    QTimer.singleShot(2000, splash.close)
    QTimer.singleShot(2000, window.show)
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
