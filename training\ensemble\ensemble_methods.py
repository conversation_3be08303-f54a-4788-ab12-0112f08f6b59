"""
Advanced Ensemble Methods for Oil Particle Detection
Combines multiple transformer models for improved accuracy
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
import json
from transformers import AutoModelForObjectDetection, AutoImageProcessor
from collections import defaultdict
import cv2

logger = logging.getLogger(__name__)


class ModelEnsemble:
    """
    Ensemble of multiple transformer models for particle detection
    Supports different combination strategies and confidence weighting
    """
    
    def __init__(self, 
                 model_configs: List[Dict],
                 ensemble_method: str = 'weighted_average',
                 confidence_threshold: float = 0.5,
                 nms_threshold: float = 0.5,
                 device: str = 'cuda'):
        """
        Args:
            model_configs: List of model configuration dictionaries
            ensemble_method: Method for combining predictions ('weighted_average', 'voting', 'stacking')
            confidence_threshold: Minimum confidence for detections
            nms_threshold: NMS threshold for duplicate removal
            device: Device to run models on
        """
        self.model_configs = model_configs
        self.ensemble_method = ensemble_method
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.device = device
        
        # Load models
        self.models = []
        self.processors = []
        self.model_weights = []
        
        self._load_models()
        
        logger.info(f"Loaded {len(self.models)} models for ensemble")
    
    def _load_models(self):
        """Load all models in the ensemble"""
        for config in self.model_configs:
            try:
                # Load model
                if 'checkpoint_path' in config:
                    model = torch.load(config['checkpoint_path'], map_location=self.device)
                else:
                    model = AutoModelForObjectDetection.from_pretrained(
                        config['model_name'],
                        num_labels=config.get('num_classes', 6)
                    )
                
                model.to(self.device)
                model.eval()
                self.models.append(model)
                
                # Load processor
                processor = AutoImageProcessor.from_pretrained(config['model_name'])
                self.processors.append(processor)
                
                # Set model weight
                weight = config.get('weight', 1.0)
                self.model_weights.append(weight)
                
                logger.info(f"Loaded model: {config['model_name']} (weight: {weight})")
                
            except Exception as e:
                logger.error(f"Failed to load model {config.get('model_name', 'unknown')}: {e}")
                continue
        
        # Normalize weights
        total_weight = sum(self.model_weights)
        self.model_weights = [w / total_weight for w in self.model_weights]
    
    def predict(self, image: np.ndarray, 
                use_tta: bool = False) -> Dict[str, torch.Tensor]:
        """
        Make ensemble prediction on an image
        
        Args:
            image: Input image as numpy array
            use_tta: Whether to use test-time augmentation
            
        Returns:
            Dictionary with ensemble predictions
        """
        if use_tta:
            return self._predict_with_tta(image)
        else:
            return self._predict_single(image)
    
    def _predict_single(self, image: np.ndarray) -> Dict[str, torch.Tensor]:
        """Make single prediction without TTA"""
        all_predictions = []
        
        # Get predictions from each model
        for i, (model, processor) in enumerate(zip(self.models, self.processors)):
            try:
                # Preprocess image
                inputs = processor(images=image, return_tensors="pt")
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                # Get prediction
                with torch.no_grad():
                    outputs = model(**inputs)
                
                # Process outputs
                predictions = self._process_model_output(outputs, i)
                all_predictions.append(predictions)
                
            except Exception as e:
                logger.warning(f"Model {i} prediction failed: {e}")
                continue
        
        # Combine predictions
        if not all_predictions:
            logger.error("No valid predictions from any model")
            return self._empty_prediction()
        
        return self._combine_predictions(all_predictions)
    
    def _predict_with_tta(self, image: np.ndarray) -> Dict[str, torch.Tensor]:
        """Make prediction with test-time augmentation"""
        from augmentations.particle_augmentations import get_test_time_augmentations
        
        tta_transforms = get_test_time_augmentations()
        all_tta_predictions = []
        
        for transform in tta_transforms:
            # Apply augmentation
            augmented = transform(image=image)
            aug_image = augmented['image']
            
            # Convert tensor back to numpy for processing
            if isinstance(aug_image, torch.Tensor):
                aug_image = aug_image.permute(1, 2, 0).numpy()
                aug_image = (aug_image * 255).astype(np.uint8)
            
            # Get prediction
            prediction = self._predict_single(aug_image)
            all_tta_predictions.append(prediction)
        
        # Average TTA predictions
        return self._average_tta_predictions(all_tta_predictions)
    
    def _process_model_output(self, outputs, model_idx: int) -> Dict[str, torch.Tensor]:
        """Process raw model output into standardized format"""
        # Extract logits and boxes
        logits = outputs.logits[0]  # Remove batch dimension
        boxes = outputs.pred_boxes[0]
        
        # Apply confidence threshold
        probs = torch.softmax(logits, dim=-1)
        max_probs, predicted_classes = torch.max(probs[:, :-1], dim=-1)  # Exclude background
        
        # Filter by confidence
        keep = max_probs > self.confidence_threshold
        
        return {
            'boxes': boxes[keep],
            'scores': max_probs[keep],
            'labels': predicted_classes[keep],
            'model_idx': torch.full((keep.sum(),), model_idx, dtype=torch.long)
        }
    
    def _combine_predictions(self, predictions: List[Dict]) -> Dict[str, torch.Tensor]:
        """Combine predictions from multiple models"""
        if self.ensemble_method == 'weighted_average':
            return self._weighted_average_combination(predictions)
        elif self.ensemble_method == 'voting':
            return self._voting_combination(predictions)
        elif self.ensemble_method == 'stacking':
            return self._stacking_combination(predictions)
        else:
            raise ValueError(f"Unknown ensemble method: {self.ensemble_method}")
    
    def _weighted_average_combination(self, predictions: List[Dict]) -> Dict[str, torch.Tensor]:
        """Combine predictions using weighted averaging"""
        # Collect all detections
        all_boxes = []
        all_scores = []
        all_labels = []
        all_weights = []
        
        for i, pred in enumerate(predictions):
            if len(pred['boxes']) > 0:
                all_boxes.append(pred['boxes'])
                all_scores.append(pred['scores'])
                all_labels.append(pred['labels'])
                
                # Repeat weight for each detection
                weight = self.model_weights[i]
                all_weights.extend([weight] * len(pred['boxes']))
        
        if not all_boxes:
            return self._empty_prediction()
        
        # Concatenate all detections
        combined_boxes = torch.cat(all_boxes, dim=0)
        combined_scores = torch.cat(all_scores, dim=0)
        combined_labels = torch.cat(all_labels, dim=0)
        combined_weights = torch.tensor(all_weights, device=self.device)
        
        # Apply weighted scores
        weighted_scores = combined_scores * combined_weights
        
        # Apply NMS
        keep_indices = self._apply_nms(combined_boxes, weighted_scores, combined_labels)
        
        return {
            'boxes': combined_boxes[keep_indices],
            'scores': weighted_scores[keep_indices],
            'labels': combined_labels[keep_indices]
        }
    
    def _voting_combination(self, predictions: List[Dict]) -> Dict[str, torch.Tensor]:
        """Combine predictions using majority voting"""
        # Group overlapping detections
        detection_groups = self._group_overlapping_detections(predictions)
        
        final_boxes = []
        final_scores = []
        final_labels = []
        
        for group in detection_groups:
            if len(group) >= len(self.models) // 2:  # Majority vote
                # Average box coordinates
                avg_box = torch.mean(torch.stack([det['box'] for det in group]), dim=0)
                
                # Weighted average score
                total_weight = sum(self.model_weights[det['model_idx']] for det in group)
                avg_score = sum(det['score'] * self.model_weights[det['model_idx']] 
                              for det in group) / total_weight
                
                # Most common label
                labels = [det['label'] for det in group]
                most_common_label = max(set(labels), key=labels.count)
                
                final_boxes.append(avg_box)
                final_scores.append(avg_score)
                final_labels.append(most_common_label)
        
        if not final_boxes:
            return self._empty_prediction()
        
        return {
            'boxes': torch.stack(final_boxes),
            'scores': torch.tensor(final_scores, device=self.device),
            'labels': torch.tensor(final_labels, device=self.device)
        }
    
    def _stacking_combination(self, predictions: List[Dict]) -> Dict[str, torch.Tensor]:
        """Combine predictions using stacking (meta-learning)"""
        # For now, fall back to weighted average
        # In a full implementation, this would use a trained meta-model
        logger.warning("Stacking not fully implemented, using weighted average")
        return self._weighted_average_combination(predictions)
    
    def _group_overlapping_detections(self, predictions: List[Dict], 
                                    iou_threshold: float = 0.5) -> List[List[Dict]]:
        """Group overlapping detections from different models"""
        all_detections = []
        
        # Collect all detections with model info
        for model_idx, pred in enumerate(predictions):
            for i in range(len(pred['boxes'])):
                all_detections.append({
                    'box': pred['boxes'][i],
                    'score': pred['scores'][i],
                    'label': pred['labels'][i],
                    'model_idx': model_idx
                })
        
        # Group overlapping detections
        groups = []
        used = set()
        
        for i, det1 in enumerate(all_detections):
            if i in used:
                continue
            
            group = [det1]
            used.add(i)
            
            for j, det2 in enumerate(all_detections[i+1:], i+1):
                if j in used:
                    continue
                
                # Check if boxes overlap and have same label
                if (det1['label'] == det2['label'] and 
                    self._calculate_iou(det1['box'], det2['box']) > iou_threshold):
                    group.append(det2)
                    used.add(j)
            
            groups.append(group)
        
        return groups
    
    def _calculate_iou(self, box1: torch.Tensor, box2: torch.Tensor) -> float:
        """Calculate IoU between two boxes"""
        # Convert from center format to corner format if needed
        if box1.shape[-1] == 4 and box1.max() <= 1.0:  # Normalized center format
            box1 = self._center_to_corner(box1)
            box2 = self._center_to_corner(box2)
        
        # Calculate intersection
        x1 = torch.max(box1[0], box2[0])
        y1 = torch.max(box1[1], box2[1])
        x2 = torch.min(box1[2], box2[2])
        y2 = torch.min(box1[3], box2[3])
        
        intersection = torch.clamp(x2 - x1, min=0) * torch.clamp(y2 - y1, min=0)
        
        # Calculate union
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        return (intersection / union).item() if union > 0 else 0.0
    
    def _center_to_corner(self, box: torch.Tensor) -> torch.Tensor:
        """Convert center format to corner format"""
        cx, cy, w, h = box
        return torch.tensor([cx - w/2, cy - h/2, cx + w/2, cy + h/2])
    
    def _apply_nms(self, boxes: torch.Tensor, scores: torch.Tensor, 
                   labels: torch.Tensor) -> torch.Tensor:
        """Apply Non-Maximum Suppression"""
        from torchvision.ops import nms
        
        # Apply NMS per class
        keep_indices = []
        
        for class_id in torch.unique(labels):
            class_mask = labels == class_id
            class_boxes = boxes[class_mask]
            class_scores = scores[class_mask]
            
            if len(class_boxes) > 0:
                # Convert to corner format for NMS
                if class_boxes.max() <= 1.0:  # Normalized format
                    class_boxes = torch.stack([
                        self._center_to_corner(box) for box in class_boxes
                    ])
                
                class_keep = nms(class_boxes, class_scores, self.nms_threshold)
                
                # Map back to original indices
                original_indices = torch.where(class_mask)[0]
                keep_indices.extend(original_indices[class_keep].tolist())
        
        return torch.tensor(keep_indices, dtype=torch.long, device=self.device)
    
    def _average_tta_predictions(self, tta_predictions: List[Dict]) -> Dict[str, torch.Tensor]:
        """Average predictions from test-time augmentation"""
        # Simple averaging for now
        # In practice, you might want to reverse the augmentations
        return self._weighted_average_combination(tta_predictions)
    
    def _empty_prediction(self) -> Dict[str, torch.Tensor]:
        """Return empty prediction"""
        return {
            'boxes': torch.empty((0, 4), device=self.device),
            'scores': torch.empty((0,), device=self.device),
            'labels': torch.empty((0,), dtype=torch.long, device=self.device)
        }


def create_ensemble_config(base_models: List[str], 
                          checkpoint_paths: Optional[List[str]] = None,
                          weights: Optional[List[float]] = None) -> List[Dict]:
    """
    Create ensemble configuration
    
    Args:
        base_models: List of base model names
        checkpoint_paths: Optional list of checkpoint paths
        weights: Optional list of model weights
        
    Returns:
        List of model configurations
    """
    configs = []
    
    for i, model_name in enumerate(base_models):
        config = {
            'model_name': model_name,
            'num_classes': 6,  # 5 particle types + background
        }
        
        if checkpoint_paths and i < len(checkpoint_paths):
            config['checkpoint_path'] = checkpoint_paths[i]
        
        if weights and i < len(weights):
            config['weight'] = weights[i]
        else:
            config['weight'] = 1.0
        
        configs.append(config)
    
    return configs


# Example ensemble configurations
PARTICLE_ENSEMBLE_CONFIGS = {
    'diverse_architectures': create_ensemble_config([
        'facebook/detr-resnet-50',
        'facebook/detr-resnet-101', 
        'IDEA-Research/dino-resnet-50'
    ], weights=[1.0, 1.2, 1.1]),
    
    'scale_ensemble': create_ensemble_config([
        'facebook/detr-resnet-50',
        'facebook/detr-resnet-50',
        'facebook/detr-resnet-50'
    ], weights=[1.0, 1.0, 1.0]),  # Different scales during training
    
    'performance_weighted': create_ensemble_config([
        'facebook/detr-resnet-50',
        'IDEA-Research/dino-resnet-50'
    ], weights=[0.8, 1.2])  # Weight by validation performance
}
