# Essential dependencies for Oil Particle Detection with RT-DETR
# Install with: pip install -r requirements_essential.txt

# Core GUI Framework
PyQt5==5.15.10

# Computer Vision and Image Processing
opencv-python==********
Pillow==10.1.0
scikit-image==0.22.0

# Deep Learning Framework - PyTorch CPU version for compatibility
torch==2.1.1+cpu
torchvision==0.16.1+cpu
torchaudio==2.1.1+cpu

# Transformers and RT-DETR
transformers==4.35.2
accelerate==0.24.1
ultralytics==8.0.206

# Scientific Computing
numpy==1.24.4
pandas==2.1.3
matplotlib==3.8.2
scipy==1.11.4

# Additional ML/AI Libraries
timm==0.9.12
supervision==0.16.0

# Utility Libraries
tqdm==4.66.1
pyyaml==6.0.1
requests==2.31.0
omegaconf==2.3.0
