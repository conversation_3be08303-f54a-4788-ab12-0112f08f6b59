# Directory Reorganization Plan

## New Clean Structure

```
oil_particle_detector_py312/
├── README.md                          # Main project documentation
├── requirements.txt                   # Consolidated requirements
├── setup.py                          # Package setup
├── main.py                           # Single entry point
├── config.yaml                       # Main configuration
│
├── oil_detector/                     # Main package
│   ├── __init__.py
│   ├── app.py                        # Main application class
│   ├── models/                       # All detection models
│   │   ├── __init__.py
│   │   ├── base_detector.py
│   │   ├── practical_detector.py
│   │   ├── rt_detr_detector.py
│   │   ├── transformer_detector.py
│   │   └── particle_analyzer.py
│   ├── gui/                          # GUI components
│   │   ├── __init__.py
│   │   ├── main_window.py
│   │   ├── detection_tab.py
│   │   ├── analysis_tab.py
│   │   └── styles.py
│   ├── core/                         # Core functionality
│   │   ├── __init__.py
│   │   ├── image_processor.py
│   │   ├── particle_classifier.py
│   │   └── results_manager.py
│   └── utils/                        # Utility functions
│       ├── __init__.py
│       ├── file_utils.py
│       ├── image_utils.py
│       └── config_utils.py
│
├── data/                             # Data directory
│   ├── samples/                      # Sample images
│   ├── annotations/                  # Annotation files
│   └── processed/                    # Processed data
│
├── results/                          # All results
│   ├── detections/                   # Detection results
│   ├── reports/                      # Generated reports
│   └── visualizations/               # Result visualizations
│
├── training/                         # Training related
│   ├── scripts/                      # Training scripts
│   ├── configs/                      # Training configurations
│   ├── checkpoints/                  # Model checkpoints
│   └── logs/                         # Training logs
│
├── tests/                            # All tests
│   ├── unit/                         # Unit tests
│   ├── integration/                  # Integration tests
│   └── fixtures/                     # Test data
│
├── docs/                             # Documentation
│   ├── user_guide/                   # User documentation
│   ├── api/                          # API documentation
│   └── development/                  # Development docs
│
├── scripts/                          # Utility scripts
│   ├── install.py                    # Installation script
│   ├── build.py                      # Build script
│   └── deploy.py                     # Deployment script
│
└── .archive/                         # Archived files (hidden)
    ├── old_versions/
    └── backup/
```

## Migration Steps

1. **Create new package structure**
2. **Consolidate models** (merge app/models + src/models)
3. **Merge results directories** (output + results)
4. **Consolidate requirements**
5. **Reorganize GUI components**
6. **Update all imports**
7. **Archive old structure**
8. **Test functionality**

## Benefits

- **Single source of truth** for each component
- **Clear separation** of concerns
- **Standard Python** package structure
- **Easy navigation** and maintenance
- **Reduced confusion** for developers
- **Better IDE support**
