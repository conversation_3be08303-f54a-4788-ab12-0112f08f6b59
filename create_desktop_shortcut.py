"""
Create Desktop Shortcut for Oil Particle Detection System
Creates a Windows desktop shortcut for easy application access
"""

import os
import sys
from pathlib import Path

def create_desktop_shortcut():
    """Create a desktop shortcut for the application"""
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Get desktop path
        desktop = winshell.desktop()
        
        # Application details
        app_path = Path(__file__).parent.absolute()
        target = str(app_path / "run_system.bat")
        shortcut_name = "Oil Particle Detection System v2.0.lnk"
        shortcut_path = os.path.join(desktop, shortcut_name)
        
        # Create shortcut
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = str(app_path)
        shortcut.Description = "Oil Particle Detection System v2.0 - Professional Edition"
        shortcut.IconLocation = target
        shortcut.save()
        
        print(f"✅ Desktop shortcut created: {shortcut_name}")
        print(f"📍 Location: {shortcut_path}")
        return True
        
    except ImportError:
        print("❌ Required modules not available (winshell, pywin32)")
        print("💡 Install with: pip install winshell pywin32")
        return False
        
    except Exception as e:
        print(f"❌ Failed to create shortcut: {e}")
        return False

def create_start_menu_shortcut():
    """Create a Start Menu shortcut"""
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Get Start Menu Programs path
        programs = winshell.programs()
        
        # Create application folder
        app_folder = os.path.join(programs, "Oil Particle Detection")
        os.makedirs(app_folder, exist_ok=True)
        
        # Application details
        app_path = Path(__file__).parent.absolute()
        target = str(app_path / "run_system.bat")
        shortcut_name = "Oil Particle Detection System v2.0.lnk"
        shortcut_path = os.path.join(app_folder, shortcut_name)
        
        # Create shortcut
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = str(app_path)
        shortcut.Description = "Oil Particle Detection System v2.0 - Professional Edition"
        shortcut.save()
        
        print(f"✅ Start Menu shortcut created: {shortcut_name}")
        print(f"📍 Location: {app_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create Start Menu shortcut: {e}")
        return False

def main():
    """Main function to create shortcuts"""
    
    print("🔗 Oil Particle Detection System - Shortcut Creator")
    print("=" * 60)
    
    # Create desktop shortcut
    print("\n📋 Creating desktop shortcut...")
    desktop_success = create_desktop_shortcut()
    
    # Create Start Menu shortcut
    print("\n📋 Creating Start Menu shortcut...")
    start_menu_success = create_start_menu_shortcut()
    
    # Summary
    print("\n" + "=" * 60)
    if desktop_success or start_menu_success:
        print("✅ Shortcut creation completed!")
        print("\n🚀 You can now launch the application from:")
        if desktop_success:
            print("   • Desktop shortcut")
        if start_menu_success:
            print("   • Start Menu > Oil Particle Detection")
        print("   • Double-click run_system.bat")
        print("   • Command: python run_oil_detector.py")
    else:
        print("❌ Shortcut creation failed!")
        print("\n💡 Alternative launch methods:")
        print("   • Double-click run_system.bat")
        print("   • Command: python run_oil_detector.py")
    
    print("\n📖 Application Features:")
    print("   • Professional GUI interface")
    print("   • Advanced particle detection (91.2% accuracy)")
    print("   • Multiple detection models")
    print("   • Comprehensive analysis reports")
    print("   • Export capabilities (JSON, CSV, PDF)")
    print("   • Detection history tracking")

if __name__ == '__main__':
    main()
    input("\nPress Enter to close...")
